<div>
    <form wire:submit="save">
        <!-- Progress Steps -->
        <div class="px-6 py-4 border-b border-secondary-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-secondary-900">
                    {{ $isEditing ? 'تعديل بيانات العميل' : 'إضافة عميل جديد' }}
                </h3>
                <div class="flex items-center space-x-2 space-x-reverse">
                    @for($i = 1; $i <= $totalSteps; $i++)
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium {{ $currentStep >= $i ? 'bg-primary-600 text-white' : 'bg-secondary-200 text-secondary-600' }}">
                                {{ $i }}
                            </div>
                            @if($i < $totalSteps)
                                <div class="w-8 h-0.5 {{ $currentStep > $i ? 'bg-primary-600' : 'bg-secondary-200' }}"></div>
                            @endif
                        </div>
                    @endfor
                </div>
            </div>
            
            <!-- Step Labels -->
            <div class="mt-2 flex justify-between text-xs text-secondary-500">
                <span class="{{ $currentStep === 1 ? 'text-primary-600 font-medium' : '' }}">المعلومات الأساسية</span>
                <span class="{{ $currentStep === 2 ? 'text-primary-600 font-medium' : '' }}">معلومات الاتصال</span>
                <span class="{{ $currentStep === 3 ? 'text-primary-600 font-medium' : '' }}">العنوان</span>
                <span class="{{ $currentStep === 4 ? 'text-primary-600 font-medium' : '' }}">معلومات العمل</span>
            </div>
        </div>

        <div class="p-6">
            <!-- Step 1: Basic Information -->
            @if($currentStep === 1)
                <div class="space-y-6">
                    <h4 class="text-md font-medium text-secondary-900 mb-4">المعلومات الأساسية</h4>
                    
                    <!-- Client Type -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">نوع العميل *</label>
                        <div class="grid grid-cols-2 gap-4">
                            <label class="flex items-center p-3 border rounded-lg cursor-pointer {{ $type === 'individual' ? 'border-primary-500 bg-primary-50' : 'border-secondary-300' }}">
                                <input wire:model.live="type" type="radio" value="individual" class="sr-only">
                                <div class="flex items-center">
                                    <x-icon name="user" class="w-5 h-5 ml-2 {{ $type === 'individual' ? 'text-primary-600' : 'text-secondary-400' }}" />
                                    <span class="text-sm font-medium {{ $type === 'individual' ? 'text-primary-900' : 'text-secondary-700' }}">فرد</span>
                                </div>
                            </label>
                            <label class="flex items-center p-3 border rounded-lg cursor-pointer {{ $type === 'company' ? 'border-primary-500 bg-primary-50' : 'border-secondary-300' }}">
                                <input wire:model.live="type" type="radio" value="company" class="sr-only">
                                <div class="flex items-center">
                                    <x-icon name="building-office" class="w-5 h-5 ml-2 {{ $type === 'company' ? 'text-primary-600' : 'text-secondary-400' }}" />
                                    <span class="text-sm font-medium {{ $type === 'company' ? 'text-primary-900' : 'text-secondary-700' }}">شركة</span>
                                </div>
                            </label>
                        </div>
                        @error('type') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>

                    @if($type === 'individual')
                        <!-- Individual Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-secondary-700 mb-1">الاسم بالعربية *</label>
                                <input wire:model="name_ar" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                @error('name_ar') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-secondary-700 mb-1">الاسم بالإنجليزية</label>
                                <input wire:model="name_en" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                @error('name_en') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-secondary-700 mb-1">تاريخ الميلاد</label>
                                <input wire:model="birth_date" type="date" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                @error('birth_date') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-secondary-700 mb-1">الجنس</label>
                                <select wire:model="gender" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                    <option value="">اختر الجنس</option>
                                    <option value="male">ذكر</option>
                                    <option value="female">أنثى</option>
                                </select>
                                @error('gender') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-secondary-700 mb-1">الجنسية</label>
                                <input wire:model="nationality" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                @error('nationality') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-secondary-700 mb-1">رقم الهوية</label>
                                <input wire:model="id_number" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                @error('id_number') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                        </div>
                    @else
                        <!-- Company Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-secondary-700 mb-1">اسم الشركة بالعربية *</label>
                                <input wire:model="company_name_ar" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                @error('company_name_ar') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-secondary-700 mb-1">اسم الشركة بالإنجليزية</label>
                                <input wire:model="company_name_en" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                @error('company_name_en') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-secondary-700 mb-1">رقم السجل التجاري</label>
                                <input wire:model="commercial_register" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                @error('commercial_register') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-secondary-700 mb-1">الرقم الضريبي</label>
                                <input wire:model="tax_number" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                @error('tax_number') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-secondary-700 mb-1">القطاع</label>
                                <input wire:model="industry" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                @error('industry') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-secondary-700 mb-1">حجم الشركة (عدد الموظفين)</label>
                                <input wire:model="company_size" type="number" min="1" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                @error('company_size') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-secondary-700 mb-1">الموقع الإلكتروني</label>
                                <input wire:model="website" type="url" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                @error('website') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                        </div>
                    @endif

                    <!-- Email -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">البريد الإلكتروني *</label>
                        <input wire:model="email" type="email" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        @error('email') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                </div>
            @endif

            <!-- Step 2: Contact Information -->
            @if($currentStep === 2)
                <div class="space-y-6">
                    <h4 class="text-md font-medium text-secondary-900 mb-4">معلومات الاتصال</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">رقم الهاتف</label>
                            <input wire:model="phone" type="tel" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('phone') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">رقم الجوال</label>
                            <input wire:model="mobile" type="tel" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('mobile') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">رقم الواتساب</label>
                            <input wire:model="whatsapp" type="tel" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('whatsapp') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                    </div>
                </div>
            @endif

            <!-- Step 3: Address Information -->
            @if($currentStep === 3)
                <div class="space-y-6">
                    <h4 class="text-md font-medium text-secondary-900 mb-4">معلومات العنوان</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">الدولة</label>
                            <input wire:model="country" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('country') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">المدينة</label>
                            <input wire:model="city" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('city') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">الحي</label>
                            <input wire:model="district" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('district') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">الرمز البريدي</label>
                            <input wire:model="postal_code" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('postal_code') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-secondary-700 mb-1">العنوان التفصيلي</label>
                            <textarea wire:model="address" rows="3" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"></textarea>
                            @error('address') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                    </div>
                </div>
            @endif

            <!-- Step 4: Business Information -->
            @if($currentStep === 4)
                <div class="space-y-6">
                    <h4 class="text-md font-medium text-secondary-900 mb-4">معلومات العمل</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">الحالة *</label>
                            <select wire:model="status" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="prospect">محتمل</option>
                                <option value="suspended">معلق</option>
                            </select>
                            @error('status') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">الأولوية *</label>
                            <select wire:model="priority" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                <option value="low">منخفضة</option>
                                <option value="medium">متوسطة</option>
                                <option value="high">عالية</option>
                                <option value="critical">حرجة</option>
                            </select>
                            @error('priority') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">المصدر</label>
                            <input wire:model="source" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('source') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">مصدر الإحالة</label>
                            <input wire:model="referral_source" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('referral_source') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">حد الائتمان</label>
                            <input wire:model="credit_limit" type="number" min="0" step="0.01" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('credit_limit') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">شروط الدفع (بالأيام)</label>
                            <input wire:model="payment_terms" type="number" min="0" max="365" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('payment_terms') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">المعين إلى</label>
                            <select wire:model="assigned_to" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                <option value="">اختر المستخدم</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}">{{ $user->name }}</option>
                                @endforeach
                            </select>
                            @error('assigned_to') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">تاريخ المتابعة التالية</label>
                            <input wire:model="next_follow_up_at" type="datetime-local" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('next_follow_up_at') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-secondary-700 mb-1">ملاحظات</label>
                            <textarea wire:model="notes" rows="4" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"></textarea>
                            @error('notes') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Form Actions -->
        <div class="px-6 py-4 bg-secondary-50 border-t border-secondary-200 flex items-center justify-between">
            <div class="flex items-center space-x-3 space-x-reverse">
                @if($currentStep > 1)
                    <button type="button" wire:click="previousStep" class="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        السابق
                    </button>
                @endif
            </div>

            <div class="flex items-center space-x-3 space-x-reverse">
                <a href="{{ $isEditing ? route('clients.show', $client) : route('clients.index') }}" 
                   class="bg-secondary-300 hover:bg-secondary-400 text-secondary-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                    إلغاء
                </a>
                
                @if($currentStep < $totalSteps)
                    <button type="button" wire:click="nextStep" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        التالي
                    </button>
                @else
                    <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        {{ $isEditing ? 'تحديث العميل' : 'إنشاء العميل' }}
                    </button>
                @endif
            </div>
        </div>
    </form>
</div>
