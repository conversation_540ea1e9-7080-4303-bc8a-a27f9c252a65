<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\ProjectTask;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ProjectTaskController extends Controller
{
    /**
     * Display a listing of project tasks.
     */
    public function index(Project $project): JsonResponse
    {
        $this->authorize('manageTasks', $project);

        $tasks = $project->tasks()
            ->with(['assignedUser', 'creator'])
            ->orderBy('order')
            ->orderBy('created_at')
            ->get();

        return response()->json($tasks);
    }

    /**
     * Store a newly created task in storage.
     */
    public function store(Request $request, Project $project): JsonResponse
    {
        $this->authorize('manageTasks', $project);

        $validated = $request->validate([
            'title_ar' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'description_ar' => 'nullable|string|max:2000',
            'description_en' => 'nullable|string|max:2000',
            'assigned_to' => 'nullable|exists:users,id',
            'priority' => 'required|in:low,medium,high,critical',
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'start_date' => 'nullable|date',
            'due_date' => 'nullable|date|after_or_equal:start_date',
            'estimated_hours' => 'nullable|integer|min:1',
            'order' => 'nullable|integer|min:0',
            'dependencies' => 'nullable|array',
            'dependencies.*' => 'exists:project_tasks,id',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
        ]);

        $validated['project_id'] = $project->id;
        $validated['created_by'] = Auth::id();

        // Set order if not provided
        if (!isset($validated['order'])) {
            $validated['order'] = $project->tasks()->max('order') + 1;
        }

        $task = ProjectTask::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'تم إضافة المهمة بنجاح',
            'task' => $task->load(['assignedUser', 'creator'])
        ]);
    }

    /**
     * Update the specified task in storage.
     */
    public function update(Request $request, Project $project, ProjectTask $task): JsonResponse
    {
        $this->authorize('manageTasks', $project);

        // Ensure the task belongs to this project
        if ($task->project_id !== $project->id) {
            abort(404);
        }

        $validated = $request->validate([
            'title_ar' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'description_ar' => 'nullable|string|max:2000',
            'description_en' => 'nullable|string|max:2000',
            'assigned_to' => 'nullable|exists:users,id',
            'priority' => 'required|in:low,medium,high,critical',
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'start_date' => 'nullable|date',
            'due_date' => 'nullable|date|after_or_equal:start_date',
            'estimated_hours' => 'nullable|integer|min:1',
            'actual_hours' => 'nullable|integer|min:0',
            'progress_percentage' => 'nullable|integer|min:0|max:100',
            'order' => 'nullable|integer|min:0',
            'dependencies' => 'nullable|array',
            'dependencies.*' => 'exists:project_tasks,id',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
            'notes' => 'nullable|string|max:2000',
        ]);

        $validated['updated_by'] = Auth::id();

        // Auto-complete task if progress is 100%
        if (isset($validated['progress_percentage']) && $validated['progress_percentage'] == 100) {
            $validated['status'] = 'completed';
            $validated['completed_at'] = now();
        }

        $task->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث المهمة بنجاح',
            'task' => $task->load(['assignedUser', 'creator'])
        ]);
    }

    /**
     * Update task status.
     */
    public function updateStatus(Request $request, Project $project, ProjectTask $task): JsonResponse
    {
        $this->authorize('manageTasks', $project);

        // Ensure the task belongs to this project
        if ($task->project_id !== $project->id) {
            abort(404);
        }

        $validated = $request->validate([
            'status' => 'required|in:pending,in_progress,completed,cancelled'
        ]);

        $updateData = [
            'status' => $validated['status'],
            'updated_by' => Auth::id()
        ];

        // Set completion date if completed
        if ($validated['status'] === 'completed') {
            $updateData['completed_at'] = now();
            $updateData['progress_percentage'] = 100;
        } elseif ($validated['status'] === 'in_progress') {
            $updateData['started_at'] = $updateData['started_at'] ?? now();
        }

        $task->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث حالة المهمة بنجاح',
            'task' => $task->load(['assignedUser', 'creator'])
        ]);
    }

    /**
     * Remove the specified task from storage.
     */
    public function destroy(Project $project, ProjectTask $task): JsonResponse
    {
        $this->authorize('manageTasks', $project);

        // Ensure the task belongs to this project
        if ($task->project_id !== $project->id) {
            abort(404);
        }

        $taskTitle = $task->display_title;
        $task->delete();

        return response()->json([
            'success' => true,
            'message' => "تم حذف المهمة {$taskTitle} بنجاح"
        ]);
    }
}
