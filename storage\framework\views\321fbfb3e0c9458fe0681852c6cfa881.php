<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['href', 'active' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['href', 'active' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$classes = $active 
    ? 'bg-primary-50 text-primary-700 group flex items-center px-3 py-2 text-sm font-medium rounded-lg border-r-2 border-primary-500'
    : 'text-secondary-500 hover:bg-secondary-50 hover:text-secondary-700 group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200';
?>

<a href="<?php echo e($href); ?>" <?php echo e($attributes->merge(['class' => $classes])); ?> wire:navigate>
    <span class="w-2 h-2 bg-current rounded-full ml-3 flex-shrink-0 opacity-50"></span>
    <span class="font-arabic"><?php echo e($slot); ?></span>
</a>
<?php /**PATH C:\laragon\www\lial_Erp\resources\views/components/nav-sub-item.blade.php ENDPATH**/ ?>