<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Invoices Table
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique()->comment('Public identifier');
            $table->string('invoice_number')->unique();
            $table->enum('type', ['invoice', 'quote', 'proforma', 'credit_note', 'debit_note'])->default('invoice');
            $table->foreignId('client_id')->constrained('clients')->onDelete('restrict');
            $table->unsignedBigInteger('project_id')->nullable()->comment('Will be constrained after projects table is created');

            // Invoice Details
            $table->date('issue_date');
            $table->date('due_date');
            $table->string('subject_ar');
            $table->string('subject_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->text('terms_ar')->nullable();
            $table->text('terms_en')->nullable();
            $table->text('notes_ar')->nullable();
            $table->text('notes_en')->nullable();

            // Financial Details
            $table->decimal('subtotal', 12, 2)->default(0);
            $table->decimal('tax_rate', 5, 2)->default(15.00)->comment('Tax rate percentage');
            $table->decimal('tax_amount', 12, 2)->default(0);
            $table->decimal('discount_rate', 5, 2)->default(0)->comment('Discount percentage');
            $table->decimal('discount_amount', 12, 2)->default(0);
            $table->decimal('total_amount', 12, 2)->default(0);
            $table->decimal('paid_amount', 12, 2)->default(0);
            $table->decimal('balance_due', 12, 2)->default(0);
            $table->string('currency', 3)->default('SAR');

            // Status and Workflow
            $table->enum('status', ['draft', 'sent', 'viewed', 'approved', 'paid', 'partially_paid', 'overdue', 'cancelled', 'refunded'])->default('draft');
            $table->enum('payment_status', ['unpaid', 'partially_paid', 'paid', 'overdue', 'cancelled'])->default('unpaid');
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('viewed_at')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('paid_at')->nullable();

            // Additional Information
            $table->json('custom_fields')->nullable();
            $table->string('pdf_path')->nullable();
            $table->boolean('is_recurring')->default(false);
            $table->foreignId('recurring_parent_id')->nullable()->constrained('invoices')->onDelete('set null');
            $table->json('recurring_settings')->nullable();

            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'status']);
            $table->index(['due_date', 'payment_status']);
            $table->index(['issue_date', 'type']);
            $table->index('payment_status');
            $table->index('is_recurring');
        });

        // Invoice Items Table
        Schema::create('invoice_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained('invoices')->onDelete('cascade');
            $table->foreignId('service_id')->nullable()->constrained('technical_services')->onDelete('set null');
            $table->string('item_name_ar');
            $table->string('item_name_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->decimal('quantity', 10, 2)->default(1);
            $table->string('unit_ar')->default('خدمة');
            $table->string('unit_en')->nullable()->default('Service');
            $table->decimal('unit_price', 10, 2);
            $table->decimal('discount_rate', 5, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('tax_rate', 5, 2)->default(15.00);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('total_amount', 10, 2);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['invoice_id', 'sort_order']);
        });

        // Payments Table
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique()->comment('Public identifier');
            $table->string('payment_number')->unique();
            $table->foreignId('client_id')->constrained('clients')->onDelete('restrict');
            $table->foreignId('invoice_id')->nullable()->constrained('invoices')->onDelete('set null');

            // Payment Details
            $table->date('payment_date');
            $table->decimal('amount', 12, 2);
            $table->string('currency', 3)->default('SAR');
            $table->enum('method', ['cash', 'bank_transfer', 'credit_card', 'debit_card', 'check', 'online', 'mobile_payment', 'other'])->default('bank_transfer');
            $table->string('reference_number')->nullable();
            $table->text('notes_ar')->nullable();
            $table->text('notes_en')->nullable();

            // Bank/Card Details
            $table->string('bank_name')->nullable();
            $table->string('account_number')->nullable();
            $table->string('transaction_id')->nullable();
            $table->json('payment_details')->nullable();

            // Status
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled', 'refunded'])->default('completed');
            $table->timestamp('processed_at')->nullable();
            $table->string('receipt_path')->nullable();

            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'payment_date']);
            $table->index(['payment_date', 'status']);
            $table->index('method');
            $table->index('status');
        });

        // Expenses Table
        Schema::create('expenses', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique()->comment('Public identifier');
            $table->string('expense_number')->unique();
            $table->unsignedBigInteger('category_id')->nullable()->comment('Will be constrained after expense_categories table is created');
            $table->foreignId('user_id')->constrained('users')->onDelete('restrict')->comment('Employee who incurred expense');
            $table->unsignedBigInteger('project_id')->nullable()->comment('Will be constrained after projects table is created');
            $table->unsignedBigInteger('client_id')->nullable()->comment('Will be constrained after clients table is created');

            // Expense Details
            $table->date('expense_date');
            $table->string('title_ar');
            $table->string('title_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('SAR');
            $table->decimal('exchange_rate', 8, 4)->default(1.0000);
            $table->decimal('amount_sar', 10, 2)->comment('Amount in SAR');

            // Payment Information
            $table->enum('payment_method', ['cash', 'credit_card', 'bank_transfer', 'check', 'petty_cash', 'company_card'])->default('cash');
            $table->string('payment_reference')->nullable();
            $table->string('vendor_name')->nullable();
            $table->string('vendor_tax_number')->nullable();
            $table->string('receipt_number')->nullable();

            // Approval Workflow
            $table->enum('status', ['draft', 'submitted', 'approved', 'rejected', 'paid', 'cancelled'])->default('draft');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('approval_notes')->nullable();
            $table->foreignId('rejected_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('rejected_at')->nullable();
            $table->text('rejection_reason')->nullable();

            // Reimbursement
            $table->boolean('is_reimbursable')->default(true);
            $table->boolean('is_reimbursed')->default(false);
            $table->date('reimbursed_date')->nullable();
            $table->string('reimbursement_reference')->nullable();

            // Attachments and Notes
            $table->json('attachments')->nullable()->comment('Receipt images and documents');
            $table->text('notes')->nullable();
            $table->json('custom_fields')->nullable();
            $table->json('tags')->nullable();

            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['user_id', 'status']);
            $table->index(['category_id', 'expense_date']);
            $table->index(['project_id', 'expense_date']);
            $table->index(['status', 'expense_date']);
            $table->index('expense_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expenses');
        Schema::dropIfExists('payments');
        Schema::dropIfExists('invoice_items');
        Schema::dropIfExists('invoices');
    }
};
