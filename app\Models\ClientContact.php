<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClientContact extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'name_ar',
        'name_en',
        'position_ar',
        'position_en',
        'email',
        'phone',
        'mobile',
        'whatsapp',
        'type',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the client that owns the contact.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the display name for the contact.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name_ar ?: $this->name_en ?: 'غير محدد';
    }

    /**
     * Get the display position for the contact.
     */
    public function getDisplayPositionAttribute(): string
    {
        return $this->position_ar ?: $this->position_en ?: '';
    }

    /**
     * Get the primary contact method.
     */
    public function getPrimaryContactAttribute(): string
    {
        return $this->mobile ?: $this->phone ?: $this->email ?: 'غير محدد';
    }

    /**
     * Scope to get only active contacts.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get primary contacts.
     */
    public function scopePrimary($query)
    {
        return $query->where('type', 'primary');
    }
}
