<?php

namespace App\Livewire\Clients;

use App\Models\Client;
use App\Models\User;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class ClientForm extends Component
{
    public Client $client;
    public $isEditing = false;

    // Form Fields
    public $type = 'individual';
    public $name_ar = '';
    public $name_en = '';
    public $email = '';
    public $phone = '';
    public $mobile = '';
    public $whatsapp = '';
    public $birth_date = '';
    public $gender = '';
    public $nationality = '';
    public $id_number = '';
    public $company_name_ar = '';
    public $company_name_en = '';
    public $commercial_register = '';
    public $tax_number = '';
    public $industry = '';
    public $company_size = '';
    public $website = '';
    public $country = 'السعودية';
    public $city = '';
    public $district = '';
    public $address = '';
    public $postal_code = '';
    public $latitude = '';
    public $longitude = '';
    public $status = 'active';
    public $priority = 'medium';
    public $source = '';
    public $referral_source = '';
    public $credit_limit = '';
    public $payment_terms = '';
    public $notes = '';
    public $tags = [];
    public $assigned_to = '';
    public $next_follow_up_at = '';

    // UI State
    public $currentStep = 1;
    public $totalSteps = 4;

    protected function rules()
    {
        $rules = [
            'type' => 'required|in:individual,company',
            'email' => 'required|email',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'whatsapp' => 'nullable|string|max:20',
            'birth_date' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female',
            'nationality' => 'nullable|string|max:100',
            'id_number' => 'nullable|string|max:20',
            'commercial_register' => 'nullable|string|max:50',
            'tax_number' => 'nullable|string|max:50',
            'industry' => 'nullable|string|max:100',
            'company_size' => 'nullable|integer|min:1',
            'website' => 'nullable|url|max:255',
            'country' => 'nullable|string|max:100',
            'city' => 'nullable|string|max:100',
            'district' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:500',
            'postal_code' => 'nullable|string|max:20',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'status' => 'required|in:active,inactive,prospect,suspended',
            'priority' => 'required|in:low,medium,high,critical',
            'source' => 'nullable|string|max:100',
            'referral_source' => 'nullable|string|max:255',
            'credit_limit' => 'nullable|numeric|min:0',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'notes' => 'nullable|string|max:1000',
            'assigned_to' => 'nullable|exists:users,id',
            'next_follow_up_at' => 'nullable|date|after:now',
        ];

        // Conditional rules based on type
        if ($this->type === 'individual') {
            $rules['name_ar'] = 'required|string|max:255';
            $rules['name_en'] = 'nullable|string|max:255';
        } else {
            $rules['company_name_ar'] = 'required|string|max:255';
            $rules['company_name_en'] = 'nullable|string|max:255';
        }

        // Unique rules for editing
        if ($this->isEditing) {
            $rules['email'] .= '|unique:clients,email,' . $this->client->id;
            if ($this->id_number) {
                $rules['id_number'] .= '|unique:clients,id_number,' . $this->client->id;
            }
            if ($this->commercial_register) {
                $rules['commercial_register'] .= '|unique:clients,commercial_register,' . $this->client->id;
            }
            if ($this->tax_number) {
                $rules['tax_number'] .= '|unique:clients,tax_number,' . $this->client->id;
            }
        } else {
            $rules['email'] .= '|unique:clients,email';
            if ($this->id_number) {
                $rules['id_number'] .= '|unique:clients,id_number';
            }
            if ($this->commercial_register) {
                $rules['commercial_register'] .= '|unique:clients,commercial_register';
            }
            if ($this->tax_number) {
                $rules['tax_number'] .= '|unique:clients,tax_number';
            }
        }

        return $rules;
    }

    protected $messages = [
        'type.required' => 'نوع العميل مطلوب',
        'name_ar.required' => 'اسم العميل مطلوب للأفراد',
        'company_name_ar.required' => 'اسم الشركة مطلوب للشركات',
        'email.required' => 'البريد الإلكتروني مطلوب',
        'email.email' => 'البريد الإلكتروني غير صحيح',
        'email.unique' => 'البريد الإلكتروني مستخدم من قبل',
        'status.required' => 'حالة العميل مطلوبة',
        'priority.required' => 'أولوية العميل مطلوبة',
    ];

    public function mount(Client $client = null)
    {
        if ($client && $client->exists) {
            $this->isEditing = true;
            $this->client = $client;
            $this->authorize('update', $client);
            $this->fillForm();
        } else {
            $this->authorize('create', Client::class);
            $this->client = new Client();
        }
    }

    public function fillForm()
    {
        $this->type = $this->client->type;
        $this->name_ar = $this->client->name_ar;
        $this->name_en = $this->client->name_en;
        $this->email = $this->client->email;
        $this->phone = $this->client->phone;
        $this->mobile = $this->client->mobile;
        $this->whatsapp = $this->client->whatsapp;
        $this->birth_date = $this->client->birth_date?->format('Y-m-d');
        $this->gender = $this->client->gender;
        $this->nationality = $this->client->nationality;
        $this->id_number = $this->client->id_number;
        $this->company_name_ar = $this->client->company_name_ar;
        $this->company_name_en = $this->client->company_name_en;
        $this->commercial_register = $this->client->commercial_register;
        $this->tax_number = $this->client->tax_number;
        $this->industry = $this->client->industry;
        $this->company_size = $this->client->company_size;
        $this->website = $this->client->website;
        $this->country = $this->client->country ?? 'السعودية';
        $this->city = $this->client->city;
        $this->district = $this->client->district;
        $this->address = $this->client->address;
        $this->postal_code = $this->client->postal_code;
        $this->latitude = $this->client->latitude;
        $this->longitude = $this->client->longitude;
        $this->status = $this->client->status;
        $this->priority = $this->client->priority;
        $this->source = $this->client->source;
        $this->referral_source = $this->client->referral_source;
        $this->credit_limit = $this->client->credit_limit;
        $this->payment_terms = $this->client->payment_terms;
        $this->notes = $this->client->notes;
        $this->tags = $this->client->tags ?? [];
        $this->assigned_to = $this->client->assigned_to;
        $this->next_follow_up_at = $this->client->next_follow_up_at?->format('Y-m-d\TH:i');
    }

    public function nextStep()
    {
        $this->validateCurrentStep();
        if ($this->currentStep < $this->totalSteps) {
            $this->currentStep++;
        }
    }

    public function previousStep()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }
    }

    public function goToStep($step)
    {
        if ($step >= 1 && $step <= $this->totalSteps) {
            $this->currentStep = $step;
        }
    }

    private function validateCurrentStep()
    {
        $rules = [];
        
        switch ($this->currentStep) {
            case 1: // Basic Information
                $rules = [
                    'type' => 'required|in:individual,company',
                    'email' => 'required|email',
                ];
                if ($this->type === 'individual') {
                    $rules['name_ar'] = 'required|string|max:255';
                } else {
                    $rules['company_name_ar'] = 'required|string|max:255';
                }
                break;
            case 2: // Contact Information
                $rules = [
                    'phone' => 'nullable|string|max:20',
                    'mobile' => 'nullable|string|max:20',
                ];
                break;
            case 3: // Address Information
                $rules = [
                    'country' => 'nullable|string|max:100',
                    'city' => 'nullable|string|max:100',
                ];
                break;
            case 4: // Business Information
                $rules = [
                    'status' => 'required|in:active,inactive,prospect,suspended',
                    'priority' => 'required|in:low,medium,high,critical',
                ];
                break;
        }

        $this->validate($rules);
    }

    public function save()
    {
        $this->validate();

        $data = [
            'type' => $this->type,
            'name_ar' => $this->name_ar,
            'name_en' => $this->name_en,
            'email' => $this->email,
            'phone' => $this->phone,
            'mobile' => $this->mobile,
            'whatsapp' => $this->whatsapp,
            'birth_date' => $this->birth_date ?: null,
            'gender' => $this->gender,
            'nationality' => $this->nationality,
            'id_number' => $this->id_number,
            'company_name_ar' => $this->company_name_ar,
            'company_name_en' => $this->company_name_en,
            'commercial_register' => $this->commercial_register,
            'tax_number' => $this->tax_number,
            'industry' => $this->industry,
            'company_size' => $this->company_size ?: null,
            'website' => $this->website,
            'country' => $this->country,
            'city' => $this->city,
            'district' => $this->district,
            'address' => $this->address,
            'postal_code' => $this->postal_code,
            'latitude' => $this->latitude ?: null,
            'longitude' => $this->longitude ?: null,
            'status' => $this->status,
            'priority' => $this->priority,
            'source' => $this->source,
            'referral_source' => $this->referral_source,
            'credit_limit' => $this->credit_limit ?: null,
            'payment_terms' => $this->payment_terms ?: null,
            'notes' => $this->notes,
            'tags' => $this->tags,
            'assigned_to' => $this->assigned_to ?: null,
            'next_follow_up_at' => $this->next_follow_up_at ?: null,
        ];

        if ($this->isEditing) {
            $data['updated_by'] = Auth::id();
            $this->client->update($data);
            $message = 'تم تحديث بيانات العميل بنجاح';
        } else {
            $data['created_by'] = Auth::id();
            $this->client = Client::create($data);
            $message = 'تم إنشاء العميل بنجاح';
        }

        session()->flash('success', $message);
        return redirect()->route('clients.show', $this->client);
    }

    public function getUsersProperty()
    {
        return User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['founder', 'admin', 'manager', 'employee']);
        })->orderBy('name')->get();
    }

    public function render()
    {
        return view('livewire.clients.client-form', [
            'users' => $this->users,
        ]);
    }
}
