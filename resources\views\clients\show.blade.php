<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="flex-shrink-0 h-12 w-12">
                    <div class="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center">
                        <span class="text-lg font-medium text-primary-600">
                            {{ substr($client->display_name, 0, 2) }}
                        </span>
                    </div>
                </div>
                <div class="mr-4">
                    <h2 class="font-semibold text-xl text-secondary-800 leading-tight">
                        {{ $client->display_name }}
                    </h2>
                    <p class="text-sm text-secondary-500">{{ $client->client_code }}</p>
                </div>
            </div>
            <div class="flex items-center space-x-2 space-x-reverse">
                @can('update', $client)
                    <a href="{{ route('clients.edit', $client) }}" 
                       class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                        <x-icon name="pencil" class="w-4 h-4 ml-2" />
                        تعديل
                    </a>
                @endcan
                
                @can('delete', $client)
                    <form method="POST" action="{{ route('clients.destroy', $client) }}" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')"
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                            <x-icon name="trash" class="w-4 h-4 ml-2" />
                            حذف
                        </button>
                    </form>
                @endcan
                
                <a href="{{ route('clients.index') }}" 
                   class="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                    <x-icon name="arrow-right" class="w-4 h-4 ml-2" />
                    العودة للقائمة
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Client Status Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="clipboard-document-list" class="w-5 h-5 text-blue-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">المشاريع النشطة</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ $client->projects->where('status', 'active')->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="banknotes" class="w-5 h-5 text-green-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">الفواتير المدفوعة</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ $client->invoices->where('payment_status', 'paid')->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="clock" class="w-5 h-5 text-yellow-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">الفواتير المعلقة</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ $client->invoices->whereIn('payment_status', ['unpaid', 'partially_paid'])->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="document-text" class="w-5 h-5 text-purple-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">المستندات</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ $client->documents->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client Profile Tabs -->
            <div class="bg-white shadow-sm rounded-lg border border-secondary-200">
                <livewire:clients.client-profile :client="$client" />
            </div>
        </div>
    </div>
</x-app-layout>
