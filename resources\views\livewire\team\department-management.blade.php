<div>
    {{-- Header --}}
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 font-arabic">إدارة الأقسام</h1>
                <p class="text-secondary-600 mt-1">إدارة أقسام الشركة والهيكل التنظيمي</p>
            </div>
            @can('create', App\Models\Department::class)
                <button wire:click="openCreateModal" 
                        class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <x-icon name="plus" class="w-5 h-5 ml-2" />
                    إضافة قسم جديد
                </button>
            @endcan
        </div>
    </div>

    {{-- Search and Filters --}}
    <div class="bg-white rounded-lg shadow-sm border border-secondary-200 p-6 mb-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <x-icon name="magnifying-glass" class="h-5 w-5 text-secondary-400" />
                    </div>
                    <input wire:model.live.debounce.300ms="search" 
                           type="text" 
                           class="block w-full pr-10 pl-3 py-2 border border-secondary-300 rounded-lg leading-5 bg-white placeholder-secondary-500 focus:outline-none focus:placeholder-secondary-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 text-sm"
                           placeholder="البحث في الأقسام...">
                </div>
            </div>
        </div>
    </div>

    {{-- Departments Table --}}
    <div class="bg-white rounded-lg shadow-sm border border-secondary-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-secondary-200">
                <thead class="bg-secondary-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            القسم
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            المدير
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            القسم الأب
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            عدد الأعضاء
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-secondary-200">
                    @forelse($departments as $department)
                        <tr class="hover:bg-secondary-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full ml-3" style="background-color: {{ $department->color }}"></div>
                                    <div>
                                        <div class="text-sm font-medium text-secondary-900">
                                            {{ $department->display_name }}
                                        </div>
                                        <div class="text-sm text-secondary-500">
                                            {{ $department->code }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($department->manager)
                                    <div class="text-sm text-secondary-900">{{ $department->manager->name }}</div>
                                @else
                                    <span class="text-secondary-400">غير محدد</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($department->parent)
                                    <div class="text-sm text-secondary-900">{{ $department->parent->display_name }}</div>
                                @else
                                    <span class="text-secondary-400">قسم رئيسي</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-secondary-900">{{ $department->team_members_count ?? 0 }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $department->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $department->is_active ? 'نشط' : 'غير نشط' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    @can('view', $department)
                                        <button wire:click="openViewModal({{ $department->id }})" 
                                                class="text-primary-600 hover:text-primary-900">
                                            <x-icon name="eye" class="w-4 h-4" />
                                        </button>
                                    @endcan
                                    
                                    @can('update', $department)
                                        <button wire:click="openEditModal({{ $department->id }})" 
                                                class="text-blue-600 hover:text-blue-900">
                                            <x-icon name="pencil" class="w-4 h-4" />
                                        </button>
                                        
                                        <button wire:click="toggleStatus({{ $department->id }})" 
                                                class="text-yellow-600 hover:text-yellow-900">
                                            <x-icon name="{{ $department->is_active ? 'eye-slash' : 'eye' }}" class="w-4 h-4" />
                                        </button>
                                    @endcan
                                    
                                    @can('delete', $department)
                                        <button wire:click="deleteDepartment({{ $department->id }})" 
                                                wire:confirm="هل أنت متأكد من حذف هذا القسم؟"
                                                class="text-red-600 hover:text-red-900">
                                            <x-icon name="trash" class="w-4 h-4" />
                                        </button>
                                    @endcan
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <x-icon name="building-office" class="w-12 h-12 text-secondary-400 mb-4" />
                                    <h3 class="text-lg font-medium text-secondary-900 mb-2">لا توجد أقسام</h3>
                                    <p class="text-secondary-500 mb-4">لم يتم العثور على أي أقسام مطابقة للبحث.</p>
                                    @can('create', App\Models\Department::class)
                                        <button wire:click="openCreateModal" 
                                                class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                            إضافة قسم جديد
                                        </button>
                                    @endcan
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        {{-- Pagination --}}
        @if($departments->hasPages())
            <div class="px-6 py-3 border-t border-secondary-200">
                {{ $departments->links() }}
            </div>
        @endif
    </div>

    {{-- Create/Edit Modal --}}
    @if($showModal)
        <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-secondary-500 bg-opacity-75 transition-opacity" wire:click="closeModal"></div>
                
                <div class="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <form wire:submit="save">
                        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                            <div class="mb-4">
                                <h3 class="text-lg font-medium text-secondary-900">
                                    {{ $editingDepartment ? 'تعديل القسم' : 'إضافة قسم جديد' }}
                                </h3>
                            </div>

                            <div class="space-y-4">
                                {{-- Arabic Name --}}
                                <div>
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        اسم القسم (عربي) <span class="text-red-500">*</span>
                                    </label>
                                    <input wire:model="name_ar" type="text" 
                                           class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                    @error('name_ar') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- English Name --}}
                                <div>
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        اسم القسم (إنجليزي)
                                    </label>
                                    <input wire:model="name_en" type="text" 
                                           class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                    @error('name_en') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- Manager --}}
                                <div>
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        مدير القسم
                                    </label>
                                    <select wire:model="manager_id" 
                                            class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                        <option value="">اختر مدير القسم</option>
                                        @foreach($managers as $manager)
                                            <option value="{{ $manager->id }}">{{ $manager->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('manager_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- Parent Department --}}
                                <div>
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        القسم الأب
                                    </label>
                                    <select wire:model="parent_id" 
                                            class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                        <option value="">قسم رئيسي</option>
                                        @foreach($parentDepartments as $parent)
                                            @if(!$editingDepartment || $parent->id !== $editingDepartment->id)
                                                <option value="{{ $parent->id }}">{{ $parent->display_name }}</option>
                                            @endif
                                        @endforeach
                                    </select>
                                    @error('parent_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- Color --}}
                                <div>
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        لون القسم <span class="text-red-500">*</span>
                                    </label>
                                    <input wire:model="color" type="color" 
                                           class="w-full h-10 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                    @error('color') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- Description --}}
                                <div>
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        وصف القسم
                                    </label>
                                    <textarea wire:model="description_ar" rows="3" 
                                              class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"></textarea>
                                    @error('description_ar') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- Active Status --}}
                                <div class="flex items-center">
                                    <input wire:model="is_active" type="checkbox" 
                                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded">
                                    <label class="mr-2 block text-sm text-secondary-900">
                                        قسم نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="bg-secondary-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                            <button type="submit" 
                                    class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mr-3 sm:w-auto sm:text-sm">
                                {{ $editingDepartment ? 'تحديث' : 'إضافة' }}
                            </button>
                            <button type="button" wire:click="closeModal" 
                                    class="mt-3 w-full inline-flex justify-center rounded-lg border border-secondary-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-secondary-700 hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:mr-3 sm:w-auto sm:text-sm">
                                إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
</div>
