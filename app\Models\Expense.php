<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Expense extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'uuid',
        'expense_number',
        'category_id',
        'user_id',
        'project_id',
        'client_id',
        'expense_date',
        'title_ar',
        'title_en',
        'description_ar',
        'description_en',
        'amount',
        'currency',
        'exchange_rate',
        'amount_sar',
        'payment_method',
        'payment_reference',
        'vendor_name',
        'vendor_tax_number',
        'receipt_number',
        'status',
        'approved_by',
        'approved_at',
        'approval_notes',
        'rejected_by',
        'rejected_at',
        'rejection_reason',
        'is_reimbursable',
        'is_reimbursed',
        'reimbursed_date',
        'reimbursement_reference',
        'attachments',
        'notes',
        'custom_fields',
        'tags',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'expense_date' => 'date',
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'amount_sar' => 'decimal:2',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
        'is_reimbursable' => 'boolean',
        'is_reimbursed' => 'boolean',
        'reimbursed_date' => 'date',
        'attachments' => 'array',
        'custom_fields' => 'array',
        'tags' => 'array',
    ];

    /**
     * Get the category this expense belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ExpenseCategory::class, 'category_id');
    }

    /**
     * Get the user who incurred this expense.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the project this expense is associated with.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the client this expense is associated with.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the user who approved this expense.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the user who rejected this expense.
     */
    public function rejector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'rejected_by');
    }

    /**
     * Get the user who created this expense.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this expense.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to filter by status.
     */
    public function scopeStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('expense_date', [$startDate, $endDate]);
    }

    /**
     * Scope to get pending approval expenses.
     */
    public function scopePendingApproval($query)
    {
        return $query->where('status', 'submitted');
    }

    /**
     * Scope to get reimbursable expenses.
     */
    public function scopeReimbursable($query)
    {
        return $query->where('is_reimbursable', true);
    }

    /**
     * Get the expense's display title.
     */
    public function getDisplayTitleAttribute(): string
    {
        return $this->title_ar ?? $this->title_en ?? 'مصروف بدون عنوان';
    }

    /**
     * Get the formatted amount with currency.
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2) . ' ' . $this->currency;
    }

    /**
     * Get the formatted SAR amount.
     */
    public function getFormattedAmountSarAttribute(): string
    {
        return number_format($this->amount_sar, 2) . ' ريال';
    }

    /**
     * Check if the expense can be approved.
     */
    public function canBeApproved(): bool
    {
        return in_array($this->status, ['submitted']);
    }

    /**
     * Check if the expense can be rejected.
     */
    public function canBeRejected(): bool
    {
        return in_array($this->status, ['submitted']);
    }

    /**
     * Check if the expense can be edited.
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, ['draft']);
    }

    /**
     * Approve the expense.
     */
    public function approve(User $approver, string $notes = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'approval_notes' => $notes,
        ]);

        return true;
    }

    /**
     * Reject the expense.
     */
    public function reject(User $rejector, string $reason): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        $this->update([
            'status' => 'rejected',
            'rejected_by' => $rejector->id,
            'rejected_at' => now(),
            'rejection_reason' => $reason,
        ]);

        return true;
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($expense) {
            if (empty($expense->uuid)) {
                $expense->uuid = Str::uuid();
            }
            if (empty($expense->expense_number)) {
                $expense->expense_number = static::generateExpenseNumber();
            }
            // Calculate SAR amount if not provided
            if (empty($expense->amount_sar)) {
                $expense->amount_sar = $expense->amount * $expense->exchange_rate;
            }
        });

        static::updating(function ($expense) {
            // Recalculate SAR amount if amount or exchange rate changed
            if ($expense->isDirty(['amount', 'exchange_rate'])) {
                $expense->amount_sar = $expense->amount * $expense->exchange_rate;
            }
        });
    }

    /**
     * Generate a unique expense number.
     */
    private static function generateExpenseNumber(): string
    {
        $prefix = 'EXP';
        $year = date('Y');
        $month = date('m');
        
        $lastExpense = static::withTrashed()
            ->where('expense_number', 'like', $prefix . $year . $month . '%')
            ->orderBy('expense_number', 'desc')
            ->first();

        if ($lastExpense) {
            $lastNumber = (int) substr($lastExpense->expense_number, strlen($prefix . $year . $month));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . $month . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
