class c{constructor(){this.refreshInterval=3e4,this.charts={},this.intervalId=null,this.init()}init(){this.initializeCharts(),this.startAutoRefresh(),this.bindEvents()}initializeCharts(){const t=document.getElementById("revenueChart");t&&(this.charts.revenue=new Chart(t.getContext("2d"),{type:"line",data:{labels:[],datasets:[{label:"الإيرادات (ريال)",data:[],borderColor:"rgb(59, 130, 246)",backgroundColor:"rgba(59, 130, 246, 0.1)",borderWidth:2,fill:!0,tension:.4}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{callback:function(s){return new Intl.NumberFormat("ar-SA").format(s)+" ريال"}}}}}}));const e=document.getElementById("projectStatusChart");e&&(this.charts.projectStatus=new Chart(e.getContext("2d"),{type:"doughnut",data:{labels:[],datasets:[{data:[],backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}}))}async refreshStats(){try{const e=await(await fetch("/api/dashboard/stats")).json();this.updateStatsDisplay(e)}catch(t){console.error("Error refreshing stats:",t)}}async refreshActivities(){try{const e=await(await fetch("/api/dashboard/activities")).json();this.updateActivitiesDisplay(e)}catch(t){console.error("Error refreshing activities:",t)}}async refreshCharts(){try{const e=await(await fetch("/api/dashboard/charts")).json();this.updateChartsDisplay(e)}catch(t){console.error("Error refreshing charts:",t)}}updateStatsDisplay(t){Object.keys(t).forEach(e=>{const s=document.querySelector(`[data-stat="${e}"]`);if(s){const r=parseInt(s.textContent.replace(/[^\d]/g,""))||0,i=t[e];this.animateValue(s,r,i)}})}updateActivitiesDisplay(t){const e=document.querySelector("[data-activities-container]");if(e){if(e.innerHTML="",t.length===0){e.innerHTML=`
                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                    </svg>
                    <p class="text-secondary-500">لا توجد أنشطة حديثة</p>
                </div>
            `;return}t.forEach(s=>{const r=document.createElement("div");r.className="flex items-start p-3 bg-secondary-50 rounded-lg hover:bg-secondary-100 transition-colors",r.innerHTML=`
                <div class="flex-shrink-0 ml-3">
                    <div class="w-8 h-8 bg-${s.color}-100 rounded-lg flex items-center justify-center">
                        <div class="w-2 h-2 bg-${s.color}-500 rounded-full"></div>
                    </div>
                </div>
                <div class="flex-1 text-right">
                    <p class="text-sm text-secondary-900 font-medium">${s.description}</p>
                    <div class="flex items-center justify-between mt-1">
                        <span class="text-xs text-secondary-500">${s.time_ago}</span>
                        <span class="text-xs text-secondary-600">${s.user_name}</span>
                    </div>
                </div>
            `,e.appendChild(r)})}}updateChartsDisplay(t){this.charts.revenue&&t.revenue_trend&&(this.charts.revenue.data.labels=t.revenue_trend.labels,this.charts.revenue.data.datasets[0].data=t.revenue_trend.data,this.charts.revenue.update("none")),this.charts.projectStatus&&t.project_status&&(this.charts.projectStatus.data.labels=t.project_status.labels,this.charts.projectStatus.data.datasets[0].data=t.project_status.data,this.charts.projectStatus.update("none"))}animateValue(t,e,s,r=1e3){const n=(s-e)/(r/16);let a=e;const o=setInterval(()=>{a+=n,(n>0&&a>=s||n<0&&a<=s)&&(a=s,clearInterval(o)),t.textContent=this.formatNumber(Math.floor(a))},16)}formatNumber(t){return new Intl.NumberFormat("ar-SA").format(t)}startAutoRefresh(){this.intervalId=setInterval(()=>{this.refreshStats(),this.refreshActivities(),this.refreshCharts()},this.refreshInterval)}stopAutoRefresh(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)}bindEvents(){const t=document.querySelector("[data-refresh-dashboard]");t&&t.addEventListener("click",()=>{this.refreshStats(),this.refreshActivities(),this.refreshCharts()}),document.addEventListener("visibilitychange",()=>{document.hidden?this.stopAutoRefresh():this.startAutoRefresh()})}destroy(){this.stopAutoRefresh(),Object.values(this.charts).forEach(t=>{t&&t.destroy()})}}document.addEventListener("DOMContentLoaded",function(){(document.querySelector("#revenueChart")||document.querySelector("#projectStatusChart"))&&(window.dashboardManager=new c)});window.addEventListener("beforeunload",function(){window.dashboardManager&&window.dashboardManager.destroy()});
