[2025-05-28 13:24:24] local.ERROR: A `view_technical_services` permission already exists for guard `web`. {"exception":"[object] (Spatie\\Permission\\Exceptions\\PermissionAlreadyExists(code: 0): A `view_technical_services` permission already exists for guard `web`. at C:\\laragon\\www\\lial_Erp\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\PermissionAlreadyExists.php:11)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php(49): Spatie\\Permission\\Exceptions\\PermissionAlreadyExists::create()
#1 C:\\laragon\\www\\lial_Erp\\database\\seeders\\RolePermissionSeeder.php(86): Spatie\\Permission\\Models\\Permission::create()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\RolePermissionSeeder->run()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#25 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#26 {main}
"} 
[2025-05-28 13:41:51] local.ERROR: SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'projects' (Connection: mysql, SQL: alter table `invoices` add constraint `invoices_project_id_foreign` foreign key (`project_id`) references `projects` (`id`) on delete set null) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'projects' (Connection: mysql, SQL: alter table `invoices` add constraint `invoices_project_id_foreign` foreign key (`project_id`) references `projects` (`id`) on delete set null) at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_134000_create_finance_tables.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'projects' at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_134000_create_finance_tables.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#33 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#34 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#36 {main}
"} 
[2025-05-28 13:43:22] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'invoices' already exists (Connection: mysql, SQL: create table `invoices` (`id` bigint unsigned not null auto_increment primary key, `uuid` char(36) not null comment 'Public identifier', `invoice_number` varchar(255) not null, `type` enum('invoice', 'quote', 'proforma', 'credit_note', 'debit_note') not null default 'invoice', `client_id` bigint unsigned not null, `project_id` bigint unsigned null comment 'Will be constrained after projects table is created', `issue_date` date not null, `due_date` date not null, `subject_ar` varchar(255) not null, `subject_en` varchar(255) null, `description_ar` text null, `description_en` text null, `terms_ar` text null, `terms_en` text null, `notes_ar` text null, `notes_en` text null, `subtotal` decimal(12, 2) not null default '0', `tax_rate` decimal(5, 2) not null default '15' comment 'Tax rate percentage', `tax_amount` decimal(12, 2) not null default '0', `discount_rate` decimal(5, 2) not null default '0' comment 'Discount percentage', `discount_amount` decimal(12, 2) not null default '0', `total_amount` decimal(12, 2) not null default '0', `paid_amount` decimal(12, 2) not null default '0', `balance_due` decimal(12, 2) not null default '0', `currency` varchar(3) not null default 'SAR', `status` enum('draft', 'sent', 'viewed', 'approved', 'paid', 'partially_paid', 'overdue', 'cancelled', 'refunded') not null default 'draft', `payment_status` enum('unpaid', 'partially_paid', 'paid', 'overdue', 'cancelled') not null default 'unpaid', `sent_at` timestamp null, `viewed_at` timestamp null, `approved_at` timestamp null, `paid_at` timestamp null, `custom_fields` json null, `pdf_path` varchar(255) null, `is_recurring` tinyint(1) not null default '0', `recurring_parent_id` bigint unsigned null, `recurring_settings` json null, `created_by` bigint unsigned not null, `updated_by` bigint unsigned null, `created_at` timestamp null, `updated_at` timestamp null, `deleted_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'invoices' already exists (Connection: mysql, SQL: create table `invoices` (`id` bigint unsigned not null auto_increment primary key, `uuid` char(36) not null comment 'Public identifier', `invoice_number` varchar(255) not null, `type` enum('invoice', 'quote', 'proforma', 'credit_note', 'debit_note') not null default 'invoice', `client_id` bigint unsigned not null, `project_id` bigint unsigned null comment 'Will be constrained after projects table is created', `issue_date` date not null, `due_date` date not null, `subject_ar` varchar(255) not null, `subject_en` varchar(255) null, `description_ar` text null, `description_en` text null, `terms_ar` text null, `terms_en` text null, `notes_ar` text null, `notes_en` text null, `subtotal` decimal(12, 2) not null default '0', `tax_rate` decimal(5, 2) not null default '15' comment 'Tax rate percentage', `tax_amount` decimal(12, 2) not null default '0', `discount_rate` decimal(5, 2) not null default '0' comment 'Discount percentage', `discount_amount` decimal(12, 2) not null default '0', `total_amount` decimal(12, 2) not null default '0', `paid_amount` decimal(12, 2) not null default '0', `balance_due` decimal(12, 2) not null default '0', `currency` varchar(3) not null default 'SAR', `status` enum('draft', 'sent', 'viewed', 'approved', 'paid', 'partially_paid', 'overdue', 'cancelled', 'refunded') not null default 'draft', `payment_status` enum('unpaid', 'partially_paid', 'paid', 'overdue', 'cancelled') not null default 'unpaid', `sent_at` timestamp null, `viewed_at` timestamp null, `approved_at` timestamp null, `paid_at` timestamp null, `custom_fields` json null, `pdf_path` varchar(255) null, `is_recurring` tinyint(1) not null default '0', `recurring_parent_id` bigint unsigned null, `recurring_settings` json null, `created_by` bigint unsigned not null, `updated_by` bigint unsigned null, `created_at` timestamp null, `updated_at` timestamp null, `deleted_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_134000_create_finance_tables.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'invoices' already exists at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_134000_create_finance_tables.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#33 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#34 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#36 {main}
"} 
[2025-05-28 13:43:34] local.ERROR: SQLSTATE[HY000]: General error: 3730 Cannot drop table 'clients' referenced by a foreign key constraint 'invoices_client_id_foreign' on table 'invoices'. (Connection: mysql, SQL: drop table if exists `clients`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 3730 Cannot drop table 'clients' referenced by a foreign key constraint 'invoices_client_id_foreign' on table 'invoices'. (Connection: mysql, SQL: drop table if exists `clients`) at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(500): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->dropIfExists()
#6 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_133819_create_clients_tables.php(167): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(337): Illuminate\\Database\\Migrations\\Migrator->runDown()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(281): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RollbackCommand.php(63): Illuminate\\Database\\Migrations\\Migrator->rollback()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\RollbackCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RollbackCommand.php(62): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RollbackCommand->handle()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#32 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#33 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 3730 Cannot drop table 'clients' referenced by a foreign key constraint 'invoices_client_id_foreign' on table 'invoices'. at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(500): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->dropIfExists()
#8 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_133819_create_clients_tables.php(167): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(337): Illuminate\\Database\\Migrations\\Migrator->runDown()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(281): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RollbackCommand.php(63): Illuminate\\Database\\Migrations\\Migrator->rollback()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\RollbackCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RollbackCommand.php(62): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RollbackCommand->handle()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#33 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#34 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#35 {main}
"} 
[2025-05-28 13:44:12] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'expenses_project_id_index' (Connection: mysql, SQL: alter table `expenses` add index `expenses_project_id_index`(`project_id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'expenses_project_id_index' (Connection: mysql, SQL: alter table `expenses` add index `expenses_project_id_index`(`project_id`)) at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#6 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_134300_add_foreign_key_constraints.php(30): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'expenses_project_id_index' at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#8 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_134300_add_foreign_key_constraints.php(30): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#33 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#34 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#36 {main}
"} 
[2025-05-28 13:45:13] local.ERROR: SQLSTATE[HY000]: General error: 1826 Duplicate foreign key constraint name 'invoices_project_id_foreign' (Connection: mysql, SQL: alter table `invoices` add constraint `invoices_project_id_foreign` foreign key (`project_id`) references `projects` (`id`) on delete set null) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1826 Duplicate foreign key constraint name 'invoices_project_id_foreign' (Connection: mysql, SQL: alter table `invoices` add constraint `invoices_project_id_foreign` foreign key (`project_id`) references `projects` (`id`) on delete set null) at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#6 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_134300_add_foreign_key_constraints.php(17): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1826 Duplicate foreign key constraint name 'invoices_project_id_foreign' at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#8 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_134300_add_foreign_key_constraints.php(17): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#33 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#34 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#36 {main}
"} 
[2025-05-28 13:45:41] local.ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR, expecting T_VARIABLE on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR, expecting T_VARIABLE on line 1 at C:\\laragon\\www\\lial_Erp\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#19 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#20 {main}
"} 
[2025-05-28 14:45:53] local.ERROR: Class "App\Http\Controllers\ClientContactController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\ClientContactController\" does not exist at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:235)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(235): ReflectionClass->__construct()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(149): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation()
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(749): array_map()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(805): Illuminate\\Support\\Arr::map()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#21 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#22 {main}
"} 
[2025-05-28 15:05:42] local.ERROR: Class "App\Http\Controllers\ProjectTaskController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\ProjectTaskController\" does not exist at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:235)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(235): ReflectionClass->__construct()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(149): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation()
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(749): array_map()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(805): Illuminate\\Support\\Arr::map()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#21 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#22 {main}
"} 
[2025-05-28 15:05:54] local.ERROR: Class "App\Http\Controllers\ProjectTaskController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\ProjectTaskController\" does not exist at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:235)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(235): ReflectionClass->__construct()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(149): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation()
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(749): array_map()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(805): Illuminate\\Support\\Arr::map()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#21 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#22 {main}
"} 
[2025-05-28 15:57:28] local.ERROR: Invalid route action: [App\Livewire\Team\TeamMemberManagement]. {"exception":"[object] (UnexpectedValueException(code: 0): Invalid route action: [App\\Livewire\\Team\\TeamMemberManagement]. at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteAction.php:92)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteAction.php(47): Illuminate\\Routing\\RouteAction::makeInvokable()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(197): Illuminate\\Routing\\RouteAction::parse()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(178): Illuminate\\Routing\\Route->parseAction()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(671): Illuminate\\Routing\\Route->__construct()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(562): Illuminate\\Routing\\Router->newRoute()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(542): Illuminate\\Routing\\Router->createRoute()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(160): Illuminate\\Routing\\Router->addRoute()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\Router->get()
#8 C:\\laragon\\www\\lial_Erp\\routes\\web.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Routing\\RouteFileRegistrar->{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group()
#12 C:\\laragon\\www\\lial_Erp\\routes\\web.php(122): Illuminate\\Routing\\RouteRegistrar->group()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Routing\\RouteFileRegistrar->{closure}()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group()
#16 C:\\laragon\\www\\lial_Erp\\routes\\web.php(121): Illuminate\\Routing\\RouteRegistrar->group()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(34): require('...')
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(512): Illuminate\\Routing\\RouteFileRegistrar->register()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(248): Illuminate\\Routing\\RouteRegistrar->group()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(162): Illuminate\\Container\\Container->call()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#33 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#34 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(143): Illuminate\\Container\\Container->call()
#35 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1153): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#36 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider()
#37 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#38 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk()
#39 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#40 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#41 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#42 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#43 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#45 {main}
"} 
[2025-05-28 16:08:05] local.ERROR: SQLSTATE[HY000]: General error: 1826 Duplicate foreign key constraint name 'invoices_project_id_foreign' (Connection: mysql, SQL: alter table `invoices` add constraint `invoices_project_id_foreign` foreign key (`project_id`) references `projects` (`id`) on delete set null) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1826 Duplicate foreign key constraint name 'invoices_project_id_foreign' (Connection: mysql, SQL: alter table `invoices` add constraint `invoices_project_id_foreign` foreign key (`project_id`) references `projects` (`id`) on delete set null) at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#6 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_134300_add_foreign_key_constraints.php(17): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1826 Duplicate foreign key constraint name 'invoices_project_id_foreign' at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#8 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_134300_add_foreign_key_constraints.php(17): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#33 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#34 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#36 {main}
"} 
[2025-05-28 16:09:32] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'category_id' doesn't exist in table (Connection: mysql, SQL: alter table `expenses` add constraint `expenses_category_id_foreign` foreign key (`category_id`) references `expense_categories` (`id`) on delete restrict) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'category_id' doesn't exist in table (Connection: mysql, SQL: alter table `expenses` add constraint `expenses_category_id_foreign` foreign key (`category_id`) references `expense_categories` (`id`) on delete restrict) at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#6 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_136000_add_foreign_key_constraints_phase6.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'category_id' doesn't exist in table at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#8 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_136000_add_foreign_key_constraints_phase6.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#33 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#34 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#36 {main}
"} 
[2025-05-28 16:10:24] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'category_id' doesn't exist in table (Connection: mysql, SQL: alter table `expenses` add constraint `expenses_category_id_foreign` foreign key (`category_id`) references `expense_categories` (`id`) on delete restrict) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'category_id' doesn't exist in table (Connection: mysql, SQL: alter table `expenses` add constraint `expenses_category_id_foreign` foreign key (`category_id`) references `expense_categories` (`id`) on delete restrict) at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#6 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_136000_add_foreign_key_constraints_phase6.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'category_id' doesn't exist in table at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#8 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_136000_add_foreign_key_constraints_phase6.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#33 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#34 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#36 {main}
"} 
[2025-05-28 16:11:28] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'category_id' doesn't exist in table (Connection: mysql, SQL: alter table `expenses` add constraint `expenses_category_id_foreign` foreign key (`category_id`) references `expense_categories` (`id`) on delete restrict) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'category_id' doesn't exist in table (Connection: mysql, SQL: alter table `expenses` add constraint `expenses_category_id_foreign` foreign key (`category_id`) references `expense_categories` (`id`) on delete restrict) at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#6 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_136000_add_foreign_key_constraints_phase6.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1072 Key column 'category_id' doesn't exist in table at C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#8 C:\\laragon\\www\\lial_Erp\\database\\migrations\\2025_05_28_136000_add_foreign_key_constraints_phase6.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#29 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#31 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#32 C:\\laragon\\www\\lial_Erp\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#33 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#34 C:\\laragon\\www\\lial_Erp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 C:\\laragon\\www\\lial_Erp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#36 {main}
"} 
