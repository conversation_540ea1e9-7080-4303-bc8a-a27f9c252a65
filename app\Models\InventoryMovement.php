<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class InventoryMovement extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'reference_number',
        'product_id',
        'user_id',
        'type',
        'reason',
        'quantity',
        'previous_stock',
        'new_stock',
        'unit_cost',
        'total_cost',
        'reference_type',
        'reference_id',
        'supplier_name',
        'customer_name',
        'location_from',
        'location_to',
        'warehouse',
        'shelf_location',
        'movement_date',
        'notes',
        'attachments',
        'is_confirmed',
        'confirmed_by',
        'confirmed_at',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'previous_stock' => 'integer',
        'new_stock' => 'integer',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'reference_id' => 'integer',
        'movement_date' => 'date',
        'attachments' => 'array',
        'is_confirmed' => 'boolean',
        'confirmed_at' => 'datetime',
    ];

    /**
     * Get the product this movement belongs to.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the user who performed this movement.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who confirmed this movement.
     */
    public function confirmer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'confirmed_by');
    }

    /**
     * Scope to filter by type.
     */
    public function scopeType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by reason.
     */
    public function scopeReason($query, string $reason)
    {
        return $query->where('reason', $reason);
    }

    /**
     * Scope to filter by product.
     */
    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('movement_date', [$startDate, $endDate]);
    }

    /**
     * Scope to get confirmed movements.
     */
    public function scopeConfirmed($query)
    {
        return $query->where('is_confirmed', true);
    }

    /**
     * Scope to get pending confirmation movements.
     */
    public function scopePendingConfirmation($query)
    {
        return $query->where('is_confirmed', false);
    }

    /**
     * Get the movement type display name.
     */
    public function getTypeDisplayAttribute(): string
    {
        return match($this->type) {
            'in' => 'إدخال',
            'out' => 'إخراج',
            'adjustment' => 'تعديل',
            'transfer' => 'نقل',
            'return' => 'إرجاع',
            'damage' => 'تلف',
            'expired' => 'منتهي الصلاحية',
            default => $this->type,
        };
    }

    /**
     * Get the movement reason display name.
     */
    public function getReasonDisplayAttribute(): string
    {
        return match($this->reason) {
            'purchase' => 'شراء',
            'sale' => 'بيع',
            'return' => 'إرجاع',
            'adjustment' => 'تعديل',
            'transfer' => 'نقل',
            'damage' => 'تلف',
            'expired' => 'منتهي الصلاحية',
            'gift' => 'هدية',
            'sample' => 'عينة',
            'other' => 'أخرى',
            default => $this->reason,
        };
    }

    /**
     * Get the formatted quantity with sign.
     */
    public function getFormattedQuantityAttribute(): string
    {
        $sign = $this->quantity >= 0 ? '+' : '';
        return $sign . number_format($this->quantity);
    }

    /**
     * Get the formatted total cost.
     */
    public function getFormattedTotalCostAttribute(): string
    {
        if ($this->total_cost === null) {
            return 'غير محدد';
        }
        return number_format($this->total_cost, 2) . ' ريال';
    }

    /**
     * Check if this movement can be confirmed.
     */
    public function canBeConfirmed(): bool
    {
        return !$this->is_confirmed;
    }

    /**
     * Confirm the movement.
     */
    public function confirm(User $confirmer = null): bool
    {
        if (!$this->canBeConfirmed()) {
            return false;
        }

        $this->update([
            'is_confirmed' => true,
            'confirmed_by' => $confirmer?->id ?? auth()->id(),
            'confirmed_at' => now(),
        ]);

        return true;
    }

    /**
     * Get the reference model if it exists.
     */
    public function getReferencedModel()
    {
        if (!$this->reference_type || !$this->reference_id) {
            return null;
        }

        $modelClass = match($this->reference_type) {
            'invoice' => Invoice::class,
            'purchase_order' => PurchaseOrder::class,
            'expense' => Expense::class,
            default => null,
        };

        if (!$modelClass || !class_exists($modelClass)) {
            return null;
        }

        return $modelClass::find($this->reference_id);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($movement) {
            if (empty($movement->uuid)) {
                $movement->uuid = Str::uuid();
            }
            if (empty($movement->reference_number)) {
                $movement->reference_number = static::generateReferenceNumber();
            }
            if ($movement->unit_cost && $movement->quantity) {
                $movement->total_cost = abs($movement->quantity) * $movement->unit_cost;
            }
        });

        static::updating(function ($movement) {
            if ($movement->isDirty(['unit_cost', 'quantity']) && $movement->unit_cost && $movement->quantity) {
                $movement->total_cost = abs($movement->quantity) * $movement->unit_cost;
            }
        });
    }

    /**
     * Generate a unique reference number.
     */
    public static function generateReferenceNumber(): string
    {
        $prefix = 'MOV';
        $year = date('Y');
        $month = date('m');
        
        $lastMovement = static::where('reference_number', 'like', $prefix . $year . $month . '%')
            ->orderBy('reference_number', 'desc')
            ->first();

        if ($lastMovement) {
            $lastNumber = (int) substr($lastMovement->reference_number, strlen($prefix . $year . $month));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . $month . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }
}
