<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreProjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create', \App\Models\Project::class);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Basic Information
            'name_ar' => ['required', 'string', 'max:255'],
            'name_en' => ['nullable', 'string', 'max:255'],
            'description_ar' => ['required', 'string', 'max:2000'],
            'description_en' => ['nullable', 'string', 'max:2000'],
            'client_id' => ['required', 'exists:clients,id'],
            'service_id' => ['nullable', 'exists:technical_services,id'],
            
            // Project Details
            'project_type' => ['required', 'in:website,mobile_app,web_app,ecommerce,custom_software,maintenance,consultation'],
            'category' => ['nullable', 'string', 'max:100'],
            'complexity_level' => ['required', 'in:simple,medium,complex,enterprise'],
            'priority' => ['required', 'in:low,medium,high,critical'],
            'status' => ['required', 'in:planning,active,on_hold,completed,cancelled'],
            
            // Timeline
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'estimated_hours' => ['nullable', 'integer', 'min:1'],
            
            // Financial
            'budget' => ['nullable', 'numeric', 'min:0'],
            'hourly_rate' => ['nullable', 'numeric', 'min:0'],
            'currency' => ['nullable', 'string', 'max:3'],
            
            // Management
            'project_manager_id' => ['nullable', 'exists:users,id'],
            'team_members' => ['nullable', 'array'],
            'team_members.*' => ['exists:users,id'],
            
            // Technical
            'technologies' => ['nullable', 'array'],
            'technologies.*' => ['string', 'max:50'],
            'requirements' => ['nullable', 'array'],
            'deliverables' => ['nullable', 'array'],
            'milestones' => ['nullable', 'array'],
            
            // Additional
            'notes_ar' => ['nullable', 'string', 'max:2000'],
            'notes_en' => ['nullable', 'string', 'max:2000'],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['string', 'max:50'],
            'custom_fields' => ['nullable', 'array'],
            'is_template' => ['boolean'],
            'template_id' => ['nullable', 'exists:projects,id'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name_ar.required' => 'اسم المشروع بالعربية مطلوب',
            'name_ar.max' => 'اسم المشروع لا يجب أن يتجاوز 255 حرف',
            'description_ar.required' => 'وصف المشروع مطلوب',
            'description_ar.max' => 'وصف المشروع لا يجب أن يتجاوز 2000 حرف',
            'client_id.required' => 'العميل مطلوب',
            'client_id.exists' => 'العميل المحدد غير موجود',
            'service_id.exists' => 'الخدمة المحددة غير موجودة',
            'project_type.required' => 'نوع المشروع مطلوب',
            'project_type.in' => 'نوع المشروع غير صحيح',
            'complexity_level.required' => 'مستوى التعقيد مطلوب',
            'complexity_level.in' => 'مستوى التعقيد غير صحيح',
            'priority.required' => 'أولوية المشروع مطلوبة',
            'priority.in' => 'أولوية المشروع غير صحيحة',
            'status.required' => 'حالة المشروع مطلوبة',
            'status.in' => 'حالة المشروع غير صحيحة',
            'start_date.required' => 'تاريخ البداية مطلوب',
            'start_date.date' => 'تاريخ البداية غير صحيح',
            'end_date.required' => 'تاريخ النهاية مطلوب',
            'end_date.date' => 'تاريخ النهاية غير صحيح',
            'end_date.after' => 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية',
            'estimated_hours.integer' => 'الساعات المقدرة يجب أن تكون رقم صحيح',
            'estimated_hours.min' => 'الساعات المقدرة يجب أن تكون أكبر من صفر',
            'budget.numeric' => 'الميزانية يجب أن تكون رقم',
            'budget.min' => 'الميزانية لا يمكن أن تكون سالبة',
            'hourly_rate.numeric' => 'السعر بالساعة يجب أن يكون رقم',
            'hourly_rate.min' => 'السعر بالساعة لا يمكن أن يكون سالب',
            'currency.max' => 'رمز العملة لا يجب أن يتجاوز 3 أحرف',
            'project_manager_id.exists' => 'مدير المشروع المحدد غير موجود',
            'team_members.array' => 'أعضاء الفريق يجب أن تكون مصفوفة',
            'team_members.*.exists' => 'أحد أعضاء الفريق المحددين غير موجود',
            'technologies.array' => 'التقنيات يجب أن تكون مصفوفة',
            'technologies.*.max' => 'كل تقنية لا يجب أن تتجاوز 50 حرف',
            'notes_ar.max' => 'الملاحظات لا يجب أن تتجاوز 2000 حرف',
            'notes_en.max' => 'الملاحظات بالإنجليزية لا يجب أن تتجاوز 2000 حرف',
            'tags.array' => 'العلامات يجب أن تكون مصفوفة',
            'tags.*.max' => 'كل علامة لا يجب أن تتجاوز 50 حرف',
            'template_id.exists' => 'القالب المحدد غير موجود',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name_ar' => 'اسم المشروع بالعربية',
            'name_en' => 'اسم المشروع بالإنجليزية',
            'description_ar' => 'وصف المشروع بالعربية',
            'description_en' => 'وصف المشروع بالإنجليزية',
            'client_id' => 'العميل',
            'service_id' => 'الخدمة',
            'project_type' => 'نوع المشروع',
            'category' => 'الفئة',
            'complexity_level' => 'مستوى التعقيد',
            'priority' => 'الأولوية',
            'status' => 'الحالة',
            'start_date' => 'تاريخ البداية',
            'end_date' => 'تاريخ النهاية',
            'estimated_hours' => 'الساعات المقدرة',
            'budget' => 'الميزانية',
            'hourly_rate' => 'السعر بالساعة',
            'currency' => 'العملة',
            'project_manager_id' => 'مدير المشروع',
            'team_members' => 'أعضاء الفريق',
            'technologies' => 'التقنيات',
            'requirements' => 'المتطلبات',
            'deliverables' => 'المخرجات',
            'milestones' => 'المعالم',
            'notes_ar' => 'الملاحظات بالعربية',
            'notes_en' => 'الملاحظات بالإنجليزية',
            'tags' => 'العلامات',
            'custom_fields' => 'الحقول المخصصة',
            'is_template' => 'قالب',
            'template_id' => 'القالب',
        ];
    }
}
