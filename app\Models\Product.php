<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'uuid',
        'sku',
        'barcode',
        'qr_code',
        'category_id',
        'name_ar',
        'name_en',
        'description_ar',
        'description_en',
        'specifications_ar',
        'specifications_en',
        'cost_price',
        'selling_price',
        'min_selling_price',
        'currency',
        'type',
        'status',
        'track_inventory',
        'current_stock',
        'min_stock_level',
        'max_stock_level',
        'reorder_quantity',
        'unit_ar',
        'unit_en',
        'weight',
        'length',
        'width',
        'height',
        'supplier_name',
        'supplier_sku',
        'supplier_price',
        'lead_time_days',
        'images',
        'tags',
        'custom_fields',
        'notes',
        'is_featured',
        'warranty_months',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'cost_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'min_selling_price' => 'decimal:2',
        'track_inventory' => 'boolean',
        'current_stock' => 'integer',
        'min_stock_level' => 'integer',
        'max_stock_level' => 'integer',
        'reorder_quantity' => 'integer',
        'weight' => 'decimal:3',
        'length' => 'decimal:2',
        'width' => 'decimal:2',
        'height' => 'decimal:2',
        'supplier_price' => 'decimal:2',
        'lead_time_days' => 'integer',
        'images' => 'array',
        'tags' => 'array',
        'custom_fields' => 'array',
        'is_featured' => 'boolean',
        'warranty_months' => 'integer',
    ];

    /**
     * Get the category this product belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    /**
     * Get the user who created this product.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this product.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get all inventory movements for this product.
     */
    public function inventoryMovements(): HasMany
    {
        return $this->hasMany(InventoryMovement::class);
    }

    /**
     * Get all stock alerts for this product.
     */
    public function stockAlerts(): HasMany
    {
        return $this->hasMany(StockAlert::class);
    }

    /**
     * Get active stock alerts for this product.
     */
    public function activeStockAlerts(): HasMany
    {
        return $this->hasMany(StockAlert::class)->where('status', 'active');
    }

    /**
     * Scope to filter by status.
     */
    public function scopeStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by type.
     */
    public function scopeType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeInCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope to get active products.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get featured products.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get products with low stock.
     */
    public function scopeLowStock($query)
    {
        return $query->where('track_inventory', true)
                    ->whereRaw('current_stock <= min_stock_level');
    }

    /**
     * Scope to get out of stock products.
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('track_inventory', true)
                    ->where('current_stock', '<=', 0);
    }

    /**
     * Get the product's display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name_ar ?? $this->name_en ?? 'منتج بدون اسم';
    }

    /**
     * Get the product's unit display name.
     */
    public function getUnitDisplayAttribute(): string
    {
        return $this->unit_ar ?? $this->unit_en ?? 'قطعة';
    }

    /**
     * Get the formatted cost price.
     */
    public function getFormattedCostPriceAttribute(): string
    {
        return number_format($this->cost_price, 2) . ' ' . $this->currency;
    }

    /**
     * Get the formatted selling price.
     */
    public function getFormattedSellingPriceAttribute(): string
    {
        return number_format($this->selling_price, 2) . ' ' . $this->currency;
    }

    /**
     * Get the total inventory value.
     */
    public function getInventoryValueAttribute(): float
    {
        return $this->current_stock * $this->cost_price;
    }

    /**
     * Get the profit margin percentage.
     */
    public function getProfitMarginAttribute(): float
    {
        if ($this->cost_price <= 0) {
            return 0;
        }
        
        return (($this->selling_price - $this->cost_price) / $this->cost_price) * 100;
    }

    /**
     * Check if the product is low on stock.
     */
    public function isLowStock(): bool
    {
        return $this->track_inventory && $this->current_stock <= $this->min_stock_level;
    }

    /**
     * Check if the product is out of stock.
     */
    public function isOutOfStock(): bool
    {
        return $this->track_inventory && $this->current_stock <= 0;
    }

    /**
     * Check if the product needs reordering.
     */
    public function needsReordering(): bool
    {
        return $this->isLowStock() && $this->reorder_quantity > 0;
    }

    /**
     * Get the primary image URL.
     */
    public function getPrimaryImageAttribute(): ?string
    {
        if (empty($this->images) || !is_array($this->images)) {
            return null;
        }
        
        return $this->images[0] ?? null;
    }

    /**
     * Update stock level.
     */
    public function updateStock(int $quantity, string $type = 'adjustment', array $movementData = []): bool
    {
        $previousStock = $this->current_stock;
        $newStock = $previousStock + $quantity;
        
        // Prevent negative stock for tracked inventory
        if ($this->track_inventory && $newStock < 0) {
            return false;
        }
        
        $this->update(['current_stock' => $newStock]);
        
        // Create inventory movement record
        InventoryMovement::create(array_merge([
            'uuid' => Str::uuid(),
            'reference_number' => InventoryMovement::generateReferenceNumber(),
            'product_id' => $this->id,
            'user_id' => auth()->id() ?? 1,
            'type' => $quantity > 0 ? 'in' : 'out',
            'reason' => $type,
            'quantity' => $quantity,
            'previous_stock' => $previousStock,
            'new_stock' => $newStock,
            'movement_date' => now()->toDateString(),
        ], $movementData));
        
        // Check for stock alerts
        $this->checkStockAlerts();
        
        return true;
    }

    /**
     * Check and create stock alerts if needed.
     */
    public function checkStockAlerts(): void
    {
        if (!$this->track_inventory) {
            return;
        }
        
        // Check for low stock alert
        if ($this->isLowStock() && !$this->stockAlerts()->where('type', 'low_stock')->where('status', 'active')->exists()) {
            StockAlert::create([
                'product_id' => $this->id,
                'type' => 'low_stock',
                'message_ar' => "المنتج {$this->display_name} منخفض المخزون",
                'message_en' => "Product {$this->display_name} is low on stock",
                'current_stock' => $this->current_stock,
                'threshold_level' => $this->min_stock_level,
                'priority' => $this->current_stock <= 0 ? 'critical' : 'high',
            ]);
        }
        
        // Check for out of stock alert
        if ($this->isOutOfStock() && !$this->stockAlerts()->where('type', 'out_of_stock')->where('status', 'active')->exists()) {
            StockAlert::create([
                'product_id' => $this->id,
                'type' => 'out_of_stock',
                'message_ar' => "المنتج {$this->display_name} نفد من المخزون",
                'message_en' => "Product {$this->display_name} is out of stock",
                'current_stock' => $this->current_stock,
                'threshold_level' => 0,
                'priority' => 'critical',
            ]);
        }
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->uuid)) {
                $product->uuid = Str::uuid();
            }
            if (empty($product->sku)) {
                $product->sku = static::generateSKU();
            }
        });

        static::updated(function ($product) {
            // Check stock alerts when stock is updated
            if ($product->isDirty('current_stock')) {
                $product->checkStockAlerts();
            }
        });
    }

    /**
     * Generate a unique SKU.
     */
    private static function generateSKU(): string
    {
        $prefix = 'PRD';
        $year = date('Y');
        $lastProduct = static::withTrashed()
            ->where('sku', 'like', $prefix . $year . '%')
            ->orderBy('sku', 'desc')
            ->first();

        if ($lastProduct) {
            $lastNumber = (int) substr($lastProduct->sku, strlen($prefix . $year));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }
}
