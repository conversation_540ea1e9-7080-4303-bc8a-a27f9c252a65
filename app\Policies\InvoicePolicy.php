<?php

namespace App\Policies;

use App\Models\Invoice;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class InvoicePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager', 'employee']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Invoice $invoice): bool
    {
        // Founders and admins can view all invoices
        if ($user->hasAnyRole(['founder', 'admin'])) {
            return true;
        }

        // Managers and employees can view invoices in their scope
        if ($user->hasAnyRole(['manager', 'employee'])) {
            return true; // For now, allow all internal users to view invoices
        }

        // Clients can view their own invoices
        if ($user->hasRole('client')) {
            return $user->email === $invoice->client->email;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager', 'employee']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Invoice $invoice): bool
    {
        // Founders and admins can update any invoice
        if ($user->hasAnyRole(['founder', 'admin'])) {
            return true;
        }

        // Managers and employees can update draft invoices they created
        if ($user->hasAnyRole(['manager', 'employee'])) {
            return $invoice->created_by === $user->id && $invoice->status === 'draft';
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Invoice $invoice): bool
    {
        // Only founders and admins can delete invoices
        if ($user->hasAnyRole(['founder', 'admin'])) {
            return true;
        }

        // Managers can delete draft invoices they created
        if ($user->hasRole('manager')) {
            return $invoice->created_by === $user->id && $invoice->status === 'draft';
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Invoice $invoice): bool
    {
        return $user->hasAnyRole(['founder', 'admin']);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Invoice $invoice): bool
    {
        return $user->hasRole('founder');
    }

    /**
     * Determine whether the user can send the invoice.
     */
    public function send(User $user, Invoice $invoice): bool
    {
        // Founders, admins, and managers can send invoices
        if ($user->hasAnyRole(['founder', 'admin', 'manager'])) {
            return true;
        }

        // Employees can send invoices they created
        if ($user->hasRole('employee')) {
            return $invoice->created_by === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can update invoice status.
     */
    public function updateStatus(User $user, Invoice $invoice): bool
    {
        // Founders and admins can update any invoice status
        if ($user->hasAnyRole(['founder', 'admin'])) {
            return true;
        }

        // Managers can update status of invoices in their scope
        if ($user->hasRole('manager')) {
            return true;
        }

        // Employees can update status of invoices they created
        if ($user->hasRole('employee')) {
            return $invoice->created_by === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can approve the invoice.
     */
    public function approve(User $user, Invoice $invoice): bool
    {
        // Only founders, admins, and managers can approve invoices
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can view invoice payments.
     */
    public function viewPayments(User $user, Invoice $invoice): bool
    {
        // Same as view permission for now
        return $this->view($user, $invoice);
    }

    /**
     * Determine whether the user can manage invoice payments.
     */
    public function managePayments(User $user, Invoice $invoice): bool
    {
        // Founders, admins, and managers can manage payments
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can download invoice PDF.
     */
    public function downloadPdf(User $user, Invoice $invoice): bool
    {
        // Same as view permission
        return $this->view($user, $invoice);
    }

    /**
     * Determine whether the user can duplicate the invoice.
     */
    public function duplicate(User $user, Invoice $invoice): bool
    {
        // Users who can create invoices can duplicate them
        return $this->create($user);
    }

    /**
     * Determine whether the user can convert invoice to different type.
     */
    public function convert(User $user, Invoice $invoice): bool
    {
        // Founders, admins, and managers can convert invoices
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can export invoice data.
     */
    public function export(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can view invoice analytics.
     */
    public function viewAnalytics(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can manage recurring invoices.
     */
    public function manageRecurring(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can refund the invoice.
     */
    public function refund(User $user, Invoice $invoice): bool
    {
        // Only founders and admins can process refunds
        return $user->hasAnyRole(['founder', 'admin']);
    }
}
