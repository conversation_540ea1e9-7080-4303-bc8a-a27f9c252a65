<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreInvoiceRequest;
use App\Http\Requests\UpdateInvoiceRequest;
use App\Models\Invoice;
use App\Models\Client;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Barryvdh\DomPDF\Facade\Pdf;

class InvoiceController extends Controller
{
    /**
     * Display a listing of invoices.
     */
    public function index(Request $request): View
    {
        $this->authorize('viewAny', Invoice::class);

        return view('invoices.index');
    }

    /**
     * Show the form for creating a new invoice.
     */
    public function create(): View
    {
        $this->authorize('create', Invoice::class);

        return view('invoices.create');
    }

    /**
     * Store a newly created invoice in storage.
     */
    public function store(StoreInvoiceRequest $request): RedirectResponse
    {
        $this->authorize('create', Invoice::class);

        $validated = $request->validated();
        $validated['created_by'] = Auth::id();

        $invoice = Invoice::create($validated);

        // Create invoice items if provided
        if (isset($validated['items']) && is_array($validated['items'])) {
            foreach ($validated['items'] as $itemData) {
                $invoice->items()->create($itemData);
            }
            $invoice->calculateTotals();
        }

        return redirect()
            ->route('invoices.show', $invoice)
            ->with('success', __('تم إنشاء الفاتورة بنجاح'));
    }

    /**
     * Display the specified invoice.
     */
    public function show(Invoice $invoice): View
    {
        $this->authorize('view', $invoice);

        $invoice->load([
            'client',
            'project',
            'items.service',
            'payments',
            'creator'
        ]);

        return view('invoices.show', compact('invoice'));
    }

    /**
     * Show the form for editing the specified invoice.
     */
    public function edit(Invoice $invoice): View
    {
        $this->authorize('update', $invoice);

        $invoice->load(['items']);

        return view('invoices.edit', compact('invoice'));
    }

    /**
     * Update the specified invoice in storage.
     */
    public function update(UpdateInvoiceRequest $request, Invoice $invoice): RedirectResponse
    {
        $this->authorize('update', $invoice);

        $validated = $request->validated();
        $validated['updated_by'] = Auth::id();

        $invoice->update($validated);

        // Update invoice items if provided
        if (isset($validated['items']) && is_array($validated['items'])) {
            // Delete existing items
            $invoice->items()->delete();
            
            // Create new items
            foreach ($validated['items'] as $itemData) {
                $invoice->items()->create($itemData);
            }
            $invoice->calculateTotals();
        }

        return redirect()
            ->route('invoices.show', $invoice)
            ->with('success', __('تم تحديث بيانات الفاتورة بنجاح'));
    }

    /**
     * Remove the specified invoice from storage.
     */
    public function destroy(Invoice $invoice): RedirectResponse
    {
        $this->authorize('delete', $invoice);

        $invoiceNumber = $invoice->invoice_number;
        $invoice->delete();

        return redirect()
            ->route('invoices.index')
            ->with('success', __('تم حذف الفاتورة :number بنجاح', ['number' => $invoiceNumber]));
    }

    /**
     * Download invoice as PDF.
     */
    public function downloadPdf(Invoice $invoice): Response
    {
        $this->authorize('view', $invoice);

        $invoice->load(['client', 'project', 'items.service']);

        $pdf = Pdf::loadView('invoices.pdf', compact('invoice'));
        
        return $pdf->download($invoice->invoice_number . '.pdf');
    }

    /**
     * Send invoice to client.
     */
    public function send(Request $request, Invoice $invoice): RedirectResponse
    {
        $this->authorize('send', $invoice);

        $request->validate([
            'email' => 'nullable|email',
            'message' => 'nullable|string|max:1000'
        ]);

        // Update invoice status
        $invoice->update([
            'status' => 'sent',
            'sent_at' => now(),
            'updated_by' => Auth::id()
        ]);

        // Here you would implement email sending logic
        // Mail::to($invoice->client->email)->send(new InvoiceMail($invoice));

        return redirect()
            ->route('invoices.show', $invoice)
            ->with('success', __('تم إرسال الفاتورة بنجاح'));
    }

    /**
     * Update invoice status.
     */
    public function updateStatus(Request $request, Invoice $invoice)
    {
        $this->authorize('updateStatus', $invoice);

        $request->validate([
            'status' => 'required|in:draft,sent,viewed,approved,paid,partially_paid,overdue,cancelled,refunded'
        ]);

        $updateData = [
            'status' => $request->status,
            'updated_by' => Auth::id()
        ];

        // Set timestamps based on status
        switch ($request->status) {
            case 'sent':
                $updateData['sent_at'] = $updateData['sent_at'] ?? now();
                break;
            case 'viewed':
                $updateData['viewed_at'] = $updateData['viewed_at'] ?? now();
                break;
            case 'approved':
                $updateData['approved_at'] = $updateData['approved_at'] ?? now();
                break;
            case 'paid':
                $updateData['paid_at'] = $updateData['paid_at'] ?? now();
                $updateData['payment_status'] = 'paid';
                break;
        }

        $invoice->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث حالة الفاتورة بنجاح'
        ]);
    }

    /**
     * Get invoices data for API/AJAX requests.
     */
    public function getInvoicesData(Request $request)
    {
        $this->authorize('viewAny', Invoice::class);

        $query = Invoice::with(['client', 'project', 'creator'])
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('invoice_number', 'like', "%{$search}%")
                      ->orWhere('subject_ar', 'like', "%{$search}%")
                      ->orWhere('subject_en', 'like', "%{$search}%")
                      ->orWhereHas('client', function ($clientQuery) use ($search) {
                          $clientQuery->where('name_ar', 'like', "%{$search}%")
                                     ->orWhere('company_name_ar', 'like', "%{$search}%");
                      });
                });
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->payment_status, function ($query, $paymentStatus) {
                $query->where('payment_status', $paymentStatus);
            })
            ->when($request->type, function ($query, $type) {
                $query->where('type', $type);
            })
            ->when($request->client_id, function ($query, $clientId) {
                $query->where('client_id', $clientId);
            })
            ->when($request->project_id, function ($query, $projectId) {
                $query->where('project_id', $projectId);
            });

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        
        $allowedSortFields = [
            'invoice_number', 'issue_date', 'due_date', 'total_amount', 
            'status', 'payment_status', 'created_at'
        ];
        
        if (in_array($sortField, $allowedSortFields)) {
            $query->orderBy($sortField, $sortDirection);
        }

        $invoices = $query->paginate($request->get('per_page', 15));

        return response()->json($invoices);
    }
}
