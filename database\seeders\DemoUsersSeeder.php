<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DemoUsersSeeder extends Seeder
{
    /**
     * Run the database seeders.
     */
    public function run(): void
    {
        // Demo users with different roles for testing
        $demoUsers = [
            [
                'name' => 'مؤسس الشركة',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'founder',
                'description' => 'حساب المؤسس - صلاحيات كاملة'
            ],
            [
                'name' => 'مدير النظام',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'admin',
                'description' => 'مدير النظام - صلاحيات إدارية'
            ],
            [
                'name' => 'مدير المشاريع',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'manager',
                'description' => 'مدير المشاريع - صلاحيات إدارة المشاريع والفرق'
            ],
            [
                'name' => 'أحمد محمد',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'employee',
                'description' => 'موظف - صلاحيات أساسية'
            ],
            [
                'name' => 'سارة أحمد',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'employee',
                'description' => 'مطورة برمجيات'
            ],
            [
                'name' => 'محمد علي',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'employee',
                'description' => 'مصمم جرافيك'
            ],
            [
                'name' => 'فاطمة خالد',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'employee',
                'description' => 'مسؤولة مبيعات'
            ],
            [
                'name' => 'عبدالله سعد',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'employee',
                'description' => 'مسؤول تسويق'
            ],
            [
                'name' => 'نورا حسن',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'employee',
                'description' => 'محاسبة'
            ],
            [
                'name' => 'شركة التقنية المتقدمة',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'client',
                'description' => 'عميل تجريبي'
            ],
            [
                'name' => 'مؤسسة الابتكار الرقمي',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'client',
                'description' => 'عميل تجريبي آخر'
            ]
        ];

        foreach ($demoUsers as $userData) {
            $user = User::firstOrCreate(
                ['email' => $userData['email']],
                [
                    'name' => $userData['name'],
                    'password' => Hash::make($userData['password']),
                    'email_verified_at' => now(),
                ]
            );

            // Assign role if it exists
            if ($user->roles()->count() === 0) {
                $user->assignRole($userData['role']);
            }
        }

        // Create additional test users for bulk testing
        $this->createBulkTestUsers();
    }

    /**
     * Create additional test users for bulk testing.
     */
    private function createBulkTestUsers(): void
    {
        // Create 10 additional employees for testing
        for ($i = 1; $i <= 10; $i++) {
            $user = User::firstOrCreate(
                ['email' => "test{$i}@lial.com"],
                [
                    'name' => "موظف تجريبي {$i}",
                    'password' => Hash::make('password123'),
                    'email_verified_at' => now(),
                ]
            );

            if ($user->roles()->count() === 0) {
                $user->assignRole('employee');
            }
        }

        // Create 5 additional clients for testing
        for ($i = 1; $i <= 5; $i++) {
            $user = User::firstOrCreate(
                ['email' => "testclient{$i}@example.com"],
                [
                    'name' => "عميل تجريبي {$i}",
                    'password' => Hash::make('password123'),
                    'email_verified_at' => now(),
                ]
            );

            if ($user->roles()->count() === 0) {
                $user->assignRole('client');
            }
        }
    }
}
