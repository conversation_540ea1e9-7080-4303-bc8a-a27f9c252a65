<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="font-semibold text-xl text-secondary-800 leading-tight">
                {{ __('إدارة الفواتير') }}
            </h2>
            <div class="flex items-center space-x-2 space-x-reverse">
                @can('create', App\Models\Invoice::class)
                    <a href="{{ route('invoices.create') }}" 
                       class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                        <x-icon name="plus" class="w-4 h-4 ml-2" />
                        إنشاء فاتورة جديدة
                    </a>
                @endcan
                
                @can('export', App\Models\Invoice::class)
                    <button type="button" 
                            class="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                        <x-icon name="arrow-down-tray" class="w-4 h-4 ml-2" />
                        تصدير
                    </button>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="document-text" class="w-5 h-5 text-blue-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">إجمالي الفواتير</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\Invoice::count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="banknotes" class="w-5 h-5 text-green-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">الفواتير المدفوعة</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\Invoice::where('payment_status', 'paid')->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="clock" class="w-5 h-5 text-yellow-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">في الانتظار</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\Invoice::whereIn('payment_status', ['unpaid', 'partially_paid'])->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="exclamation-triangle" class="w-5 h-5 text-red-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">متأخرة</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\Invoice::where('status', 'overdue')->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Revenue Summary -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-secondary-900 mb-4">إجمالي الإيرادات</h3>
                        <div class="text-3xl font-bold text-green-600">
                            {{ number_format(\App\Models\Invoice::where('payment_status', 'paid')->sum('total_amount'), 2) }} ريال
                        </div>
                        <p class="text-sm text-secondary-500 mt-2">من الفواتير المدفوعة</p>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-secondary-900 mb-4">في الانتظار</h3>
                        <div class="text-3xl font-bold text-yellow-600">
                            {{ number_format(\App\Models\Invoice::whereIn('payment_status', ['unpaid', 'partially_paid'])->sum('total_amount'), 2) }} ريال
                        </div>
                        <p class="text-sm text-secondary-500 mt-2">مبالغ غير مدفوعة</p>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-secondary-900 mb-4">هذا الشهر</h3>
                        <div class="text-3xl font-bold text-blue-600">
                            {{ number_format(\App\Models\Invoice::whereMonth('created_at', now()->month)->sum('total_amount'), 2) }} ريال
                        </div>
                        <p class="text-sm text-secondary-500 mt-2">إجمالي فواتير الشهر</p>
                    </div>
                </div>
            </div>

            <!-- Invoice List Component -->
            <div class="bg-white shadow-sm rounded-lg border border-secondary-200">
                <livewire:invoices.invoice-list />
            </div>
        </div>
    </div>
</x-app-layout>
