<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreProjectRequest;
use App\Http\Requests\UpdateProjectRequest;
use App\Models\Project;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class ProjectController extends Controller
{
    /**
     * Display a listing of projects.
     */
    public function index(Request $request): View
    {
        $this->authorize('viewAny', Project::class);

        return view('projects.index');
    }

    /**
     * Show the form for creating a new project.
     */
    public function create(): View
    {
        $this->authorize('create', Project::class);

        return view('projects.create');
    }

    /**
     * Store a newly created project in storage.
     */
    public function store(StoreProjectRequest $request): RedirectResponse
    {
        $this->authorize('create', Project::class);

        $validated = $request->validated();
        $validated['created_by'] = Auth::id();

        $project = Project::create($validated);

        return redirect()
            ->route('projects.show', $project)
            ->with('success', __('تم إنشاء المشروع بنجاح'));
    }

    /**
     * Display the specified project.
     */
    public function show(Project $project): View
    {
        $this->authorize('view', $project);

        $project->load([
            'client',
            'projectManager',
            'tasks' => function ($query) {
                $query->with(['assignedUser'])->latest();
            },
            'files' => function ($query) {
                $query->latest()->limit(10);
            },
            'creator'
        ]);

        return view('projects.show', compact('project'));
    }

    /**
     * Show the form for editing the specified project.
     */
    public function edit(Project $project): View
    {
        $this->authorize('update', $project);

        return view('projects.edit', compact('project'));
    }

    /**
     * Update the specified project in storage.
     */
    public function update(UpdateProjectRequest $request, Project $project): RedirectResponse
    {
        $this->authorize('update', $project);

        $validated = $request->validated();
        $validated['updated_by'] = Auth::id();

        $project->update($validated);

        return redirect()
            ->route('projects.show', $project)
            ->with('success', __('تم تحديث بيانات المشروع بنجاح'));
    }

    /**
     * Remove the specified project from storage.
     */
    public function destroy(Project $project): RedirectResponse
    {
        $this->authorize('delete', $project);

        $projectName = $project->display_name;
        $project->delete();

        return redirect()
            ->route('projects.index')
            ->with('success', __('تم حذف المشروع :name بنجاح', ['name' => $projectName]));
    }

    /**
     * Get projects data for API/AJAX requests.
     */
    public function getProjectsData(Request $request)
    {
        $this->authorize('viewAny', Project::class);

        $query = Project::with(['client', 'projectManager', 'creator'])
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name_ar', 'like', "%{$search}%")
                      ->orWhere('name_en', 'like', "%{$search}%")
                      ->orWhere('project_code', 'like', "%{$search}%")
                      ->orWhereHas('client', function ($clientQuery) use ($search) {
                          $clientQuery->where('name_ar', 'like', "%{$search}%")
                                     ->orWhere('company_name_ar', 'like', "%{$search}%");
                      });
                });
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->priority, function ($query, $priority) {
                $query->where('priority', $priority);
            })
            ->when($request->client_id, function ($query, $clientId) {
                $query->where('client_id', $clientId);
            })
            ->when($request->project_manager_id, function ($query, $managerId) {
                $query->where('project_manager_id', $managerId);
            })
            ->when($request->project_type, function ($query, $type) {
                $query->where('project_type', $type);
            });

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        
        $allowedSortFields = [
            'name_ar', 'name_en', 'project_code', 'status', 'priority', 
            'start_date', 'end_date', 'progress_percentage', 'created_at'
        ];
        
        if (in_array($sortField, $allowedSortFields)) {
            $query->orderBy($sortField, $sortDirection);
        }

        $projects = $query->paginate($request->get('per_page', 15));

        return response()->json($projects);
    }

    /**
     * Update project status.
     */
    public function updateStatus(Request $request, Project $project)
    {
        $this->authorize('update', $project);

        $request->validate([
            'status' => 'required|in:planning,active,on_hold,completed,cancelled'
        ]);

        $project->update([
            'status' => $request->status,
            'updated_by' => Auth::id()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث حالة المشروع بنجاح'
        ]);
    }

    /**
     * Update project progress.
     */
    public function updateProgress(Request $request, Project $project)
    {
        $this->authorize('update', $project);

        $request->validate([
            'progress_percentage' => 'required|integer|min:0|max:100'
        ]);

        $project->update([
            'progress_percentage' => $request->progress_percentage,
            'updated_by' => Auth::id()
        ]);

        // Auto-update status based on progress
        if ($request->progress_percentage == 100 && $project->status !== 'completed') {
            $project->update(['status' => 'completed']);
        } elseif ($request->progress_percentage > 0 && $project->status === 'planning') {
            $project->update(['status' => 'active']);
        }

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث تقدم المشروع بنجاح'
        ]);
    }
}
