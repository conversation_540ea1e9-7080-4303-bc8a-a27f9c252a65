{{-- Print Logo Component --}}
@props([
    'size' => 'default', // small, default, large
    'showText' => true,
    'class' => ''
])

@php
    // Size configurations for print
    $sizeClasses = [
        'small' => 'height: 1.5cm; width: auto;',
        'default' => 'height: 2.5cm; width: auto;',
        'large' => 'height: 3.5cm; width: auto;'
    ];
    
    $logoStyle = $sizeClasses[$size] ?? $sizeClasses['default'];
    
    // Text size configurations
    $textSizes = [
        'small' => ['title' => 'font-size: 14pt;', 'subtitle' => 'font-size: 10pt;'],
        'default' => ['title' => 'font-size: 18pt;', 'subtitle' => 'font-size: 12pt;'],
        'large' => ['title' => 'font-size: 22pt;', 'subtitle' => 'font-size: 14pt;']
    ];
    
    $textStyle = $textSizes[$size] ?? $textSizes['default'];
    
    // Check for logo file existence
    $hasLogo = file_exists(public_path('images/logo.png'));
    $logoUrl = $hasLogo ? asset('images/logo.png') : null;
@endphp

<div class="print-logo {{ $class }}" style="display: flex; align-items: center; justify-content: center; margin: 0; padding: 0;">
    @if($hasLogo)
        <img src="{{ $logoUrl }}" 
             alt="{{ __('app.company.name') }}" 
             style="{{ $logoStyle }} object-fit: contain; display: block; print-color-adjust: exact; -webkit-print-color-adjust: exact;">
    @else
        {{-- Fallback for print - simple text logo --}}
        <div style="text-align: center; font-family: 'Times New Roman', serif;">
            <div style="{{ $textStyle['title'] }} font-weight: bold; color: #000; margin: 0; padding: 0;">
                {{ __('app.company.name_short', [], 'ar') ?? 'ليال' }}
            </div>
            @if($showText)
                <div style="{{ $textStyle['subtitle'] }} color: #333; margin: 0; padding: 0;">
                    {{ __('app.company.tagline_short', [], 'ar') ?? 'للتطوير البرمجي' }}
                </div>
            @endif
        </div>
    @endif
    
    @if($showText && $hasLogo)
        <div style="margin-right: 15px; text-align: right; font-family: 'Times New Roman', serif;">
            <div style="{{ $textStyle['title'] }} font-weight: bold; color: #000; margin: 0; padding: 0;">
                {{ __('app.company.name_short', [], 'ar') ?? 'ليال' }}
            </div>
            <div style="{{ $textStyle['subtitle'] }} color: #333; margin: 0; padding: 0;">
                {{ __('app.company.tagline_short', [], 'ar') ?? 'للتطوير البرمجي' }}
            </div>
        </div>
    @endif
</div>

{{-- Print-specific styles --}}
<style>
    @media print {
        .print-logo {
            break-inside: avoid;
            page-break-inside: avoid;
        }
        
        .print-logo img {
            print-color-adjust: exact;
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
        }
    }
</style>
