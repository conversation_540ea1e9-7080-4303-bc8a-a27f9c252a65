<?php

namespace App\Livewire\Team;

use App\Models\Department;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class DepartmentManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $showModal = false;
    public $editingDepartment = null;
    public $selectedDepartment = null;

    // Form fields
    public $code = '';
    public $name_ar = '';
    public $name_en = '';
    public $description_ar = '';
    public $description_en = '';
    public $color = '#3b82f6';
    public $icon = '';
    public $manager_id = '';
    public $parent_id = '';
    public $is_active = true;
    public $sort_order = 0;

    protected $rules = [
        'name_ar' => 'required|string|max:255',
        'name_en' => 'nullable|string|max:255',
        'description_ar' => 'nullable|string',
        'description_en' => 'nullable|string',
        'color' => 'required|string|size:7',
        'icon' => 'nullable|string|max:50',
        'manager_id' => 'nullable|exists:users,id',
        'parent_id' => 'nullable|exists:departments,id',
        'is_active' => 'boolean',
        'sort_order' => 'integer|min:0',
    ];

    protected $messages = [
        'name_ar.required' => 'اسم القسم باللغة العربية مطلوب',
        'color.required' => 'لون القسم مطلوب',
        'color.size' => 'لون القسم يجب أن يكون بصيغة صحيحة',
        'manager_id.exists' => 'المدير المحدد غير موجود',
        'parent_id.exists' => 'القسم الأب المحدد غير موجود',
    ];

    public function mount()
    {
        $this->authorize('viewAny', Department::class);
    }

    public function render()
    {
        $departments = Department::with(['manager', 'parent', 'children'])
            ->when($this->search, function ($query) {
                $query->where('name_ar', 'like', '%' . $this->search . '%')
                      ->orWhere('name_en', 'like', '%' . $this->search . '%')
                      ->orWhere('code', 'like', '%' . $this->search . '%');
            })
            ->orderBy('sort_order')
            ->orderBy('name_ar')
            ->paginate(15);

        $managers = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['founder', 'admin', 'manager']);
        })->get();

        $parentDepartments = Department::whereNull('parent_id')
            ->where('is_active', true)
            ->orderBy('name_ar')
            ->get();

        return view('livewire.team.department-management', [
            'departments' => $departments,
            'managers' => $managers,
            'parentDepartments' => $parentDepartments,
        ]);
    }

    public function openCreateModal()
    {
        $this->authorize('create', Department::class);
        $this->resetForm();
        $this->showModal = true;
    }

    public function openEditModal(Department $department)
    {
        $this->authorize('update', $department);
        $this->editingDepartment = $department;
        $this->fillForm($department);
        $this->showModal = true;
    }

    public function openViewModal(Department $department)
    {
        $this->authorize('view', $department);
        $this->selectedDepartment = $department->load(['manager', 'parent', 'children', 'teamMembers']);
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->selectedDepartment = null;
        $this->resetForm();
    }

    public function save()
    {
        $this->validate();

        if ($this->editingDepartment) {
            $this->authorize('update', $this->editingDepartment);
            $this->editingDepartment->update($this->getFormData());
            $message = 'تم تحديث القسم بنجاح';
        } else {
            $this->authorize('create', Department::class);
            Department::create($this->getFormData());
            $message = 'تم إنشاء القسم بنجاح';
        }

        $this->closeModal();
        session()->flash('message', $message);
        $this->dispatch('department-saved');
    }

    public function deleteDepartment(Department $department)
    {
        $this->authorize('delete', $department);

        if ($department->children()->exists()) {
            session()->flash('error', 'لا يمكن حذف قسم يحتوي على أقسام فرعية');
            return;
        }

        if ($department->teamMembers()->exists()) {
            session()->flash('error', 'لا يمكن حذف قسم يحتوي على أعضاء فريق');
            return;
        }

        $department->delete();
        session()->flash('message', 'تم حذف القسم بنجاح');
    }

    public function toggleStatus(Department $department)
    {
        $this->authorize('update', $department);
        $department->update(['is_active' => !$department->is_active]);
        
        $status = $department->is_active ? 'تفعيل' : 'إلغاء تفعيل';
        session()->flash('message', "تم {$status} القسم بنجاح");
    }

    private function resetForm()
    {
        $this->editingDepartment = null;
        $this->code = '';
        $this->name_ar = '';
        $this->name_en = '';
        $this->description_ar = '';
        $this->description_en = '';
        $this->color = '#3b82f6';
        $this->icon = '';
        $this->manager_id = '';
        $this->parent_id = '';
        $this->is_active = true;
        $this->sort_order = 0;
        $this->resetErrorBag();
    }

    private function fillForm(Department $department)
    {
        $this->code = $department->code;
        $this->name_ar = $department->name_ar;
        $this->name_en = $department->name_en;
        $this->description_ar = $department->description_ar;
        $this->description_en = $department->description_en;
        $this->color = $department->color;
        $this->icon = $department->icon;
        $this->manager_id = $department->manager_id;
        $this->parent_id = $department->parent_id;
        $this->is_active = $department->is_active;
        $this->sort_order = $department->sort_order;
    }

    private function getFormData(): array
    {
        return [
            'name_ar' => $this->name_ar,
            'name_en' => $this->name_en,
            'description_ar' => $this->description_ar,
            'description_en' => $this->description_en,
            'color' => $this->color,
            'icon' => $this->icon,
            'manager_id' => $this->manager_id ?: null,
            'parent_id' => $this->parent_id ?: null,
            'is_active' => $this->is_active,
            'sort_order' => $this->sort_order,
        ];
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }
}
