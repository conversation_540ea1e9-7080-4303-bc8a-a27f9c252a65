<div>
    <!-- Tab Navigation -->
    <div class="border-b border-secondary-200">
        <nav class="-mb-px flex space-x-8 space-x-reverse px-6" aria-label="Tabs">
            <button wire:click="setActiveTab('overview')" 
                    class="py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'overview' ? 'border-primary-500 text-primary-600' : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300' }}">
                نظرة عامة
            </button>
            <button wire:click="setActiveTab('contact')" 
                    class="py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'contact' ? 'border-primary-500 text-primary-600' : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300' }}">
                معلومات الاتصال
            </button>
            <button wire:click="setActiveTab('projects')" 
                    class="py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'projects' ? 'border-primary-500 text-primary-600' : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300' }}">
                المشاريع
            </button>
            <button wire:click="setActiveTab('invoices')" 
                    class="py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'invoices' ? 'border-primary-500 text-primary-600' : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300' }}">
                الفواتير
            </button>
            <button wire:click="setActiveTab('documents')" 
                    class="py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'documents' ? 'border-primary-500 text-primary-600' : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300' }}">
                المستندات
            </button>
            <button wire:click="setActiveTab('communications')" 
                    class="py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'communications' ? 'border-primary-500 text-primary-600' : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300' }}">
                التواصل
            </button>
        </nav>
    </div>

    <!-- Tab Content -->
    <div class="p-6">
        @if($activeTab === 'overview')
            <!-- Overview Tab -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Basic Information -->
                <div>
                    <h3 class="text-lg font-medium text-secondary-900 mb-4">المعلومات الأساسية</h3>
                    <div class="bg-secondary-50 rounded-lg p-4 space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-secondary-500">النوع:</span>
                            <span class="text-sm text-secondary-900">{{ $client->type === 'individual' ? 'فرد' : 'شركة' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-secondary-500">البريد الإلكتروني:</span>
                            <span class="text-sm text-secondary-900">{{ $client->email }}</span>
                        </div>
                        @if($client->phone)
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-secondary-500">الهاتف:</span>
                                <span class="text-sm text-secondary-900">{{ $client->phone }}</span>
                            </div>
                        @endif
                        @if($client->mobile)
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-secondary-500">الجوال:</span>
                                <span class="text-sm text-secondary-900">{{ $client->mobile }}</span>
                            </div>
                        @endif
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-secondary-500">الحالة:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $client->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                {{ $client->status === 'active' ? 'نشط' : ($client->status === 'inactive' ? 'غير نشط' : ($client->status === 'prospect' ? 'محتمل' : 'معلق')) }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-secondary-500">الأولوية:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $client->priority === 'high' ? 'bg-red-100 text-red-800' : ($client->priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                                {{ $client->priority === 'high' ? 'عالية' : ($client->priority === 'medium' ? 'متوسطة' : ($client->priority === 'low' ? 'منخفضة' : 'حرجة')) }}
                            </span>
                        </div>
                        @if($client->assignedUser)
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-secondary-500">المعين إلى:</span>
                                <span class="text-sm text-secondary-900">{{ $client->assignedUser->name }}</span>
                            </div>
                        @endif
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-secondary-500">تاريخ الإنشاء:</span>
                            <span class="text-sm text-secondary-900">{{ $client->created_at->format('Y/m/d H:i') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Company/Individual Specific Info -->
                <div>
                    @if($client->type === 'company')
                        <h3 class="text-lg font-medium text-secondary-900 mb-4">معلومات الشركة</h3>
                        <div class="bg-secondary-50 rounded-lg p-4 space-y-3">
                            @if($client->commercial_register)
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-secondary-500">السجل التجاري:</span>
                                    <span class="text-sm text-secondary-900">{{ $client->commercial_register }}</span>
                                </div>
                            @endif
                            @if($client->tax_number)
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-secondary-500">الرقم الضريبي:</span>
                                    <span class="text-sm text-secondary-900">{{ $client->tax_number }}</span>
                                </div>
                            @endif
                            @if($client->industry)
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-secondary-500">القطاع:</span>
                                    <span class="text-sm text-secondary-900">{{ $client->industry }}</span>
                                </div>
                            @endif
                            @if($client->company_size)
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-secondary-500">حجم الشركة:</span>
                                    <span class="text-sm text-secondary-900">{{ $client->company_size }} موظف</span>
                                </div>
                            @endif
                            @if($client->website)
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-secondary-500">الموقع:</span>
                                    <a href="{{ $client->website }}" target="_blank" class="text-sm text-primary-600 hover:text-primary-800">{{ $client->website }}</a>
                                </div>
                            @endif
                        </div>
                    @else
                        <h3 class="text-lg font-medium text-secondary-900 mb-4">المعلومات الشخصية</h3>
                        <div class="bg-secondary-50 rounded-lg p-4 space-y-3">
                            @if($client->birth_date)
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-secondary-500">تاريخ الميلاد:</span>
                                    <span class="text-sm text-secondary-900">{{ $client->birth_date->format('Y/m/d') }}</span>
                                </div>
                            @endif
                            @if($client->gender)
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-secondary-500">الجنس:</span>
                                    <span class="text-sm text-secondary-900">{{ $client->gender === 'male' ? 'ذكر' : 'أنثى' }}</span>
                                </div>
                            @endif
                            @if($client->nationality)
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-secondary-500">الجنسية:</span>
                                    <span class="text-sm text-secondary-900">{{ $client->nationality }}</span>
                                </div>
                            @endif
                            @if($client->id_number)
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-secondary-500">رقم الهوية:</span>
                                    <span class="text-sm text-secondary-900">{{ $client->id_number }}</span>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>
            </div>

            <!-- Address Information -->
            @if($client->full_address)
                <div class="mt-8">
                    <h3 class="text-lg font-medium text-secondary-900 mb-4">العنوان</h3>
                    <div class="bg-secondary-50 rounded-lg p-4">
                        <p class="text-sm text-secondary-900">{{ $client->full_address }}</p>
                    </div>
                </div>
            @endif

            <!-- Notes -->
            @if($client->notes)
                <div class="mt-8">
                    <h3 class="text-lg font-medium text-secondary-900 mb-4">الملاحظات</h3>
                    <div class="bg-secondary-50 rounded-lg p-4">
                        <p class="text-sm text-secondary-900">{{ $client->notes }}</p>
                    </div>
                </div>
            @endif

        @elseif($activeTab === 'contact')
            <!-- Contact Information Tab -->
            <div>
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-secondary-900">معلومات الاتصال</h3>
                    @can('manageContacts', $client)
                        <button class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                            إضافة جهة اتصال
                        </button>
                    @endcan
                </div>

                @if($client->contacts->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($client->contacts as $contact)
                            <div class="bg-white border border-secondary-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="font-medium text-secondary-900">{{ $contact->name }}</h4>
                                    @can('manageContacts', $client)
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-secondary-400 hover:text-secondary-600">
                                                <x-icon name="pencil" class="w-4 h-4" />
                                            </button>
                                            <button class="text-red-400 hover:text-red-600">
                                                <x-icon name="trash" class="w-4 h-4" />
                                            </button>
                                        </div>
                                    @endcan
                                </div>
                                @if($contact->position)
                                    <p class="text-sm text-secondary-500 mb-2">{{ $contact->position }}</p>
                                @endif
                                @if($contact->email)
                                    <p class="text-sm text-secondary-700 mb-1">{{ $contact->email }}</p>
                                @endif
                                @if($contact->phone)
                                    <p class="text-sm text-secondary-700">{{ $contact->phone }}</p>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <x-icon name="users" class="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-secondary-900 mb-2">لا توجد جهات اتصال</h3>
                        <p class="text-secondary-500">لم يتم إضافة أي جهات اتصال لهذا العميل بعد.</p>
                    </div>
                @endif
            </div>

        @elseif($activeTab === 'projects')
            <!-- Projects Tab -->
            <div>
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-secondary-900">المشاريع</h3>
                    <button class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        إضافة مشروع جديد
                    </button>
                </div>

                @if($client->projects->count() > 0)
                    <div class="space-y-4">
                        @foreach($client->projects as $project)
                            <div class="bg-white border border-secondary-200 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium text-secondary-900">{{ $project->name_ar }}</h4>
                                        <p class="text-sm text-secondary-500">{{ $project->description_ar }}</p>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $project->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                        {{ $project->status }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <x-icon name="clipboard-document-list" class="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-secondary-900 mb-2">لا توجد مشاريع</h3>
                        <p class="text-secondary-500">لم يتم إنشاء أي مشاريع لهذا العميل بعد.</p>
                    </div>
                @endif
            </div>

        @elseif($activeTab === 'invoices')
            <!-- Invoices Tab -->
            <div>
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-secondary-900">الفواتير</h3>
                    <button class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        إنشاء فاتورة جديدة
                    </button>
                </div>

                @if($client->invoices->count() > 0)
                    <div class="space-y-4">
                        @foreach($client->invoices as $invoice)
                            <div class="bg-white border border-secondary-200 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium text-secondary-900">{{ $invoice->invoice_number }}</h4>
                                        <p class="text-sm text-secondary-500">{{ $invoice->created_at->format('Y/m/d') }}</p>
                                    </div>
                                    <div class="text-left">
                                        <p class="font-medium text-secondary-900">{{ number_format($invoice->total_amount, 2) }} ريال</p>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $invoice->payment_status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                            {{ $invoice->payment_status === 'paid' ? 'مدفوعة' : 'معلقة' }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <x-icon name="banknotes" class="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-secondary-900 mb-2">لا توجد فواتير</h3>
                        <p class="text-secondary-500">لم يتم إنشاء أي فواتير لهذا العميل بعد.</p>
                    </div>
                @endif
            </div>

        @elseif($activeTab === 'documents')
            <!-- Documents Tab -->
            <div>
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-secondary-900">المستندات</h3>
                    @can('uploadDocuments', $client)
                        <button class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                            رفع مستند
                        </button>
                    @endcan
                </div>

                @if($client->documents->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($client->documents as $document)
                            <div class="bg-white border border-secondary-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-secondary-900 truncate">{{ $document->name_ar }}</h4>
                                    @can('manageDocuments', $client)
                                        <button class="text-red-400 hover:text-red-600">
                                            <x-icon name="trash" class="w-4 h-4" />
                                        </button>
                                    @endcan
                                </div>
                                <p class="text-sm text-secondary-500 mb-3">{{ $document->type }}</p>
                                <button class="w-full bg-secondary-100 hover:bg-secondary-200 text-secondary-700 px-3 py-2 rounded text-sm font-medium transition-colors duration-200">
                                    تحميل
                                </button>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <x-icon name="document-text" class="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-secondary-900 mb-2">لا توجد مستندات</h3>
                        <p class="text-secondary-500">لم يتم رفع أي مستندات لهذا العميل بعد.</p>
                    </div>
                @endif
            </div>

        @elseif($activeTab === 'communications')
            <!-- Communications Tab -->
            <div>
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-secondary-900">سجل التواصل</h3>
                    @can('createCommunications', $client)
                        <button class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                            إضافة تواصل
                        </button>
                    @endcan
                </div>

                @if($client->communications->count() > 0)
                    <div class="space-y-4">
                        @foreach($client->communications as $communication)
                            <div class="bg-white border border-secondary-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-secondary-900">{{ $communication->type }}</h4>
                                    <span class="text-sm text-secondary-500">{{ $communication->created_at->format('Y/m/d H:i') }}</span>
                                </div>
                                <p class="text-sm text-secondary-700">{{ $communication->notes }}</p>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <x-icon name="chat-bubble-left-right" class="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-secondary-900 mb-2">لا يوجد سجل تواصل</h3>
                        <p class="text-secondary-500">لم يتم تسجيل أي تواصل مع هذا العميل بعد.</p>
                    </div>
                @endif
            </div>
        @endif
    </div>
</div>
