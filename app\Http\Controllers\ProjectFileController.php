<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\ProjectFile;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ProjectFileController extends Controller
{
    /**
     * Display a listing of project files.
     */
    public function index(Project $project): JsonResponse
    {
        $this->authorize('viewFiles', $project);

        $files = $project->files()
            ->with(['uploader'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($files);
    }

    /**
     * Store a newly created file in storage.
     */
    public function store(Request $request, Project $project): JsonResponse
    {
        $this->authorize('manageFiles', $project);

        $validated = $request->validate([
            'name_ar' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'description_ar' => 'nullable|string|max:1000',
            'description_en' => 'nullable|string|max:1000',
            'file' => 'required|file|max:51200', // 50MB max
            'category' => 'nullable|string|max:100',
            'version' => 'nullable|string|max:20',
            'is_public' => 'boolean',
        ]);

        // Handle file upload
        $file = $request->file('file');
        $fileName = time() . '_' . $file->getClientOriginalName();
        $filePath = $file->storeAs('project-files/' . $project->id, $fileName, 'private');

        $fileData = [
            'project_id' => $project->id,
            'name_ar' => $validated['name_ar'],
            'name_en' => $validated['name_en'],
            'description_ar' => $validated['description_ar'],
            'description_en' => $validated['description_en'],
            'file_path' => $filePath,
            'file_name' => $fileName,
            'original_name' => $file->getClientOriginalName(),
            'file_type' => $file->getClientMimeType(),
            'file_size' => $file->getSize(),
            'category' => $validated['category'],
            'version' => $validated['version'] ?? '1.0',
            'is_public' => $validated['is_public'] ?? false,
            'uploaded_by' => Auth::id(),
        ];

        $projectFile = ProjectFile::create($fileData);

        return response()->json([
            'success' => true,
            'message' => 'تم رفع الملف بنجاح',
            'file' => $projectFile->load('uploader')
        ]);
    }

    /**
     * Download the specified file.
     */
    public function download(Project $project, ProjectFile $file): Response
    {
        $this->authorize('viewFiles', $project);

        // Ensure the file belongs to this project
        if ($file->project_id !== $project->id) {
            abort(404);
        }

        if (!Storage::disk('private')->exists($file->file_path)) {
            abort(404, 'الملف غير موجود');
        }

        return Storage::disk('private')->download($file->file_path, $file->original_name);
    }

    /**
     * Remove the specified file from storage.
     */
    public function destroy(Project $project, ProjectFile $file): JsonResponse
    {
        $this->authorize('manageFiles', $project);

        // Ensure the file belongs to this project
        if ($file->project_id !== $project->id) {
            abort(404);
        }

        $fileName = $file->name_ar;

        // Delete the file from storage
        if (Storage::disk('private')->exists($file->file_path)) {
            Storage::disk('private')->delete($file->file_path);
        }

        $file->delete();

        return response()->json([
            'success' => true,
            'message' => "تم حذف الملف {$fileName} بنجاح"
        ]);
    }
}
