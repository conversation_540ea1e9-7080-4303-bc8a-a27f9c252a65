<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Product Categories Table
        Schema::create('product_categories', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique()->comment('Category code');
            $table->string('name_ar');
            $table->string('name_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->string('color', 7)->default('#3b82f6');
            $table->string('icon')->nullable();
            $table->string('image_path')->nullable();
            $table->foreignId('parent_id')->nullable()->constrained('product_categories')->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['is_active', 'sort_order']);
            $table->index('parent_id');
        });

        // Products Table
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->string('sku')->unique()->comment('Stock Keeping Unit');
            $table->string('barcode')->nullable()->unique();
            $table->string('qr_code')->nullable();
            $table->foreignId('category_id')->constrained('product_categories')->onDelete('restrict');
            
            // Product Information
            $table->string('name_ar');
            $table->string('name_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->text('specifications_ar')->nullable();
            $table->text('specifications_en')->nullable();
            
            // Pricing
            $table->decimal('cost_price', 10, 2)->default(0)->comment('Purchase cost');
            $table->decimal('selling_price', 10, 2)->default(0)->comment('Selling price');
            $table->decimal('min_selling_price', 10, 2)->nullable()->comment('Minimum allowed selling price');
            $table->string('currency', 3)->default('SAR');
            
            // Inventory Management
            $table->enum('type', ['product', 'service', 'digital', 'bundle'])->default('product');
            $table->enum('status', ['active', 'inactive', 'discontinued', 'out_of_stock'])->default('active');
            $table->boolean('track_inventory')->default(true);
            $table->integer('current_stock')->default(0);
            $table->integer('min_stock_level')->default(0)->comment('Reorder level');
            $table->integer('max_stock_level')->nullable()->comment('Maximum stock level');
            $table->integer('reorder_quantity')->default(0)->comment('Quantity to reorder');
            $table->string('unit_ar')->default('قطعة');
            $table->string('unit_en')->nullable()->default('Piece');
            
            // Physical Properties
            $table->decimal('weight', 8, 3)->nullable()->comment('Weight in kg');
            $table->decimal('length', 8, 2)->nullable()->comment('Length in cm');
            $table->decimal('width', 8, 2)->nullable()->comment('Width in cm');
            $table->decimal('height', 8, 2)->nullable()->comment('Height in cm');
            
            // Supplier Information
            $table->string('supplier_name')->nullable();
            $table->string('supplier_sku')->nullable();
            $table->decimal('supplier_price', 10, 2)->nullable();
            $table->integer('lead_time_days')->nullable()->comment('Supplier lead time');
            
            // Additional Information
            $table->json('images')->nullable()->comment('Product images');
            $table->json('tags')->nullable();
            $table->json('custom_fields')->nullable();
            $table->text('notes')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->integer('warranty_months')->nullable();
            
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['category_id', 'status']);
            $table->index(['status', 'track_inventory']);
            $table->index(['current_stock', 'min_stock_level']);
            $table->index('type');
            $table->index('is_featured');
        });

        // Inventory Movements Table
        Schema::create('inventory_movements', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->string('reference_number')->unique();
            $table->foreignId('product_id')->constrained('products')->onDelete('restrict');
            $table->foreignId('user_id')->constrained('users')->onDelete('restrict')->comment('User who performed the movement');
            
            // Movement Details
            $table->enum('type', ['in', 'out', 'adjustment', 'transfer', 'return', 'damage', 'expired'])->comment('Movement type');
            $table->enum('reason', ['purchase', 'sale', 'return', 'adjustment', 'transfer', 'damage', 'expired', 'gift', 'sample', 'other'])->default('other');
            $table->integer('quantity')->comment('Positive for in, negative for out');
            $table->integer('previous_stock')->comment('Stock before movement');
            $table->integer('new_stock')->comment('Stock after movement');
            $table->decimal('unit_cost', 10, 2)->nullable()->comment('Cost per unit for this movement');
            $table->decimal('total_cost', 10, 2)->nullable()->comment('Total cost of movement');
            
            // Reference Information
            $table->string('reference_type')->nullable()->comment('invoice, purchase_order, etc.');
            $table->unsignedBigInteger('reference_id')->nullable()->comment('ID of referenced document');
            $table->string('supplier_name')->nullable();
            $table->string('customer_name')->nullable();
            
            // Location Information
            $table->string('location_from')->nullable();
            $table->string('location_to')->nullable();
            $table->string('warehouse')->nullable();
            $table->string('shelf_location')->nullable();
            
            // Additional Information
            $table->date('movement_date');
            $table->text('notes')->nullable();
            $table->json('attachments')->nullable();
            $table->boolean('is_confirmed')->default(true);
            $table->foreignId('confirmed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('confirmed_at')->nullable();
            
            $table->timestamps();
            
            $table->index(['product_id', 'movement_date']);
            $table->index(['type', 'movement_date']);
            $table->index(['reference_type', 'reference_id']);
            $table->index('movement_date');
        });

        // Stock Alerts Table
        Schema::create('stock_alerts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->enum('type', ['low_stock', 'out_of_stock', 'overstock', 'expiry_warning'])->comment('Alert type');
            $table->enum('status', ['active', 'acknowledged', 'resolved'])->default('active');
            $table->text('message_ar');
            $table->text('message_en')->nullable();
            $table->integer('current_stock')->comment('Stock level when alert was created');
            $table->integer('threshold_level')->nullable()->comment('The threshold that triggered this alert');
            $table->date('expiry_date')->nullable()->comment('For expiry warnings');
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            
            // Alert Management
            $table->foreignId('acknowledged_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('acknowledged_at')->nullable();
            $table->text('acknowledgment_notes')->nullable();
            $table->foreignId('resolved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('resolved_at')->nullable();
            $table->text('resolution_notes')->nullable();
            
            $table->timestamps();
            
            $table->index(['product_id', 'status']);
            $table->index(['type', 'status']);
            $table->index(['priority', 'status']);
            $table->index('status');
        });

        // Inventory Adjustments Table
        Schema::create('inventory_adjustments', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->string('adjustment_number')->unique();
            $table->date('adjustment_date');
            $table->enum('type', ['physical_count', 'damage', 'theft', 'expired', 'correction', 'other'])->default('physical_count');
            $table->enum('status', ['draft', 'pending_approval', 'approved', 'rejected'])->default('draft');
            
            // Approval Workflow
            $table->foreignId('requested_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('approval_notes')->nullable();
            
            // Additional Information
            $table->text('reason_ar');
            $table->text('reason_en')->nullable();
            $table->text('notes')->nullable();
            $table->json('attachments')->nullable();
            
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['adjustment_date', 'status']);
            $table->index(['type', 'status']);
            $table->index('requested_by');
        });

        // Inventory Adjustment Items Table
        Schema::create('inventory_adjustment_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('adjustment_id')->constrained('inventory_adjustments')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('restrict');
            $table->integer('system_quantity')->comment('Quantity according to system');
            $table->integer('physical_quantity')->comment('Actual physical quantity');
            $table->integer('difference')->comment('Difference (physical - system)');
            $table->decimal('unit_cost', 10, 2)->nullable();
            $table->decimal('total_cost_impact', 10, 2)->nullable()->comment('Financial impact of adjustment');
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->index(['adjustment_id', 'product_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_adjustment_items');
        Schema::dropIfExists('inventory_adjustments');
        Schema::dropIfExists('stock_alerts');
        Schema::dropIfExists('inventory_movements');
        Schema::dropIfExists('products');
        Schema::dropIfExists('product_categories');
    }
};
