<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Expense Categories Table
        Schema::create('expense_categories', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique()->comment('Category code');
            $table->string('name_ar');
            $table->string('name_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->string('color', 7)->default('#6b7280');
            $table->string('icon')->nullable();
            $table->foreignId('parent_id')->nullable()->constrained('expense_categories')->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->boolean('requires_approval')->default(false);
            $table->decimal('approval_limit', 10, 2)->nullable()->comment('Amount requiring approval');
            $table->json('allowed_roles')->nullable()->comment('Roles allowed to use this category');
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['is_active', 'sort_order']);
            $table->index('parent_id');
        });

        // Budget Categories Table
        Schema::create('budget_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar');
            $table->string('name_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->foreignId('expense_category_id')->nullable()->constrained('expense_categories')->onDelete('set null');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });

        // Budgets Table
        Schema::create('budgets', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->string('budget_number')->unique();
            $table->string('name_ar');
            $table->string('name_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->foreignId('budget_category_id')->constrained('budget_categories')->onDelete('restrict');
            $table->foreignId('department_id')->nullable()->constrained('departments')->onDelete('set null');
            $table->foreignId('project_id')->nullable()->constrained('projects')->onDelete('set null');
            
            // Budget Period
            $table->enum('period_type', ['monthly', 'quarterly', 'yearly', 'custom'])->default('monthly');
            $table->date('start_date');
            $table->date('end_date');
            $table->year('fiscal_year');
            
            // Budget Amounts
            $table->decimal('allocated_amount', 12, 2)->comment('Total allocated budget');
            $table->decimal('spent_amount', 12, 2)->default(0)->comment('Amount spent so far');
            $table->decimal('committed_amount', 12, 2)->default(0)->comment('Amount committed but not spent');
            $table->decimal('available_amount', 12, 2)->comment('Available amount');
            $table->string('currency', 3)->default('SAR');
            
            // Alert Settings
            $table->decimal('warning_threshold', 5, 2)->default(80.00)->comment('Warning at % of budget');
            $table->decimal('critical_threshold', 5, 2)->default(95.00)->comment('Critical at % of budget');
            $table->boolean('enable_alerts')->default(true);
            
            // Status and Approval
            $table->enum('status', ['draft', 'active', 'suspended', 'closed', 'cancelled'])->default('draft');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('approval_notes')->nullable();
            
            // Additional Information
            $table->text('notes')->nullable();
            $table->json('custom_fields')->nullable();
            
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['budget_category_id', 'status']);
            $table->index(['start_date', 'end_date']);
            $table->index(['fiscal_year', 'period_type']);
            $table->index('status');
        });

        // Budget Allocations Table (for detailed budget breakdown)
        Schema::create('budget_allocations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('budget_id')->constrained('budgets')->onDelete('cascade');
            $table->foreignId('expense_category_id')->constrained('expense_categories')->onDelete('restrict');
            $table->string('allocation_name_ar');
            $table->string('allocation_name_en')->nullable();
            $table->decimal('allocated_amount', 10, 2);
            $table->decimal('spent_amount', 10, 2)->default(0);
            $table->decimal('available_amount', 10, 2);
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->index(['budget_id', 'expense_category_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('budget_allocations');
        Schema::dropIfExists('budgets');
        Schema::dropIfExists('budget_categories');
        Schema::dropIfExists('expense_categories');
    }
};
