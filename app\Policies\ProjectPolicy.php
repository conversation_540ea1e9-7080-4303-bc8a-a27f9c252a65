<?php

namespace App\Policies;

use App\Models\Project;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ProjectPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager', 'employee']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Project $project): bool
    {
        // Founders and admins can view all projects
        if ($user->hasAny<PERSON>ole(['founder', 'admin'])) {
            return true;
        }

        // Project managers can view their projects
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // Team members can view projects they're assigned to
        if (is_array($project->team_members) && in_array($user->id, $project->team_members)) {
            return true;
        }

        // Managers and employees can view projects in their department/scope
        if ($user->hasAnyRole(['manager', 'employee'])) {
            return true; // For now, allow all internal users to view projects
        }

        // Clients can view their own projects
        if ($user->hasRole('client')) {
            return $user->email === $project->client->email;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Project $project): bool
    {
        // Founders and admins can update any project
        if ($user->hasAnyRole(['founder', 'admin'])) {
            return true;
        }

        // Project managers can update their projects
        if ($project->project_manager_id === $user->id) {
            return true;
        }

        // Managers can update projects in their scope
        if ($user->hasRole('manager')) {
            return true; // For now, allow managers to update all projects
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Project $project): bool
    {
        // Only founders and admins can delete projects
        return $user->hasAnyRole(['founder', 'admin']);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Project $project): bool
    {
        return $user->hasAnyRole(['founder', 'admin']);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Project $project): bool
    {
        return $user->hasRole('founder');
    }

    /**
     * Determine whether the user can manage project tasks.
     */
    public function manageTasks(User $user, Project $project): bool
    {
        // Founders, admins, and project managers can manage tasks
        if ($user->hasAnyRole(['founder', 'admin']) || $project->project_manager_id === $user->id) {
            return true;
        }

        // Team members can manage tasks assigned to them
        if (is_array($project->team_members) && in_array($user->id, $project->team_members)) {
            return true;
        }

        // Managers can manage tasks in their projects
        if ($user->hasRole('manager')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can manage project files.
     */
    public function manageFiles(User $user, Project $project): bool
    {
        // Founders, admins, project managers, and team members can manage files
        if ($user->hasAnyRole(['founder', 'admin']) || 
            $project->project_manager_id === $user->id ||
            (is_array($project->team_members) && in_array($user->id, $project->team_members))) {
            return true;
        }

        // Managers can manage files in their projects
        if ($user->hasRole('manager')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view project files.
     */
    public function viewFiles(User $user, Project $project): bool
    {
        // Same as view permission for now
        return $this->view($user, $project);
    }

    /**
     * Determine whether the user can assign team members to the project.
     */
    public function assignTeamMembers(User $user, Project $project): bool
    {
        // Founders, admins, and project managers can assign team members
        return $user->hasAnyRole(['founder', 'admin']) || $project->project_manager_id === $user->id;
    }

    /**
     * Determine whether the user can update project status.
     */
    public function updateStatus(User $user, Project $project): bool
    {
        // Founders, admins, and project managers can update status
        return $user->hasAnyRole(['founder', 'admin']) || $project->project_manager_id === $user->id;
    }

    /**
     * Determine whether the user can view project financials.
     */
    public function viewFinancials(User $user, Project $project): bool
    {
        // Founders, admins, and project managers can view financials
        if ($user->hasAnyRole(['founder', 'admin']) || $project->project_manager_id === $user->id) {
            return true;
        }

        // Managers can view financials for their projects
        if ($user->hasRole('manager')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can update project financials.
     */
    public function updateFinancials(User $user, Project $project): bool
    {
        // Only founders and admins can update financials
        return $user->hasAnyRole(['founder', 'admin']);
    }

    /**
     * Determine whether the user can approve project deliverables.
     */
    public function approveDeliverables(User $user, Project $project): bool
    {
        // Founders, admins, and project managers can approve deliverables
        return $user->hasAnyRole(['founder', 'admin']) || $project->project_manager_id === $user->id;
    }

    /**
     * Determine whether the user can export project data.
     */
    public function export(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can create project templates.
     */
    public function createTemplates(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }
}
