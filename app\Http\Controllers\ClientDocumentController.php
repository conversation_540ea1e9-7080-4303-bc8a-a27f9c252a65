<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\ClientDocument;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ClientDocumentController extends Controller
{
    /**
     * Display a listing of client documents.
     */
    public function index(Client $client): JsonResponse
    {
        $this->authorize('manageDocuments', $client);

        $documents = $client->documents()->orderBy('created_at', 'desc')->get();

        return response()->json($documents);
    }

    /**
     * Store a newly created document in storage.
     */
    public function store(Request $request, Client $client): JsonResponse
    {
        $this->authorize('uploadDocuments', $client);

        $validated = $request->validate([
            'name_ar' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'type' => 'required|string|max:100',
            'description_ar' => 'nullable|string|max:1000',
            'description_en' => 'nullable|string|max:1000',
            'file' => 'required|file|max:10240', // 10MB max
            'expiry_date' => 'nullable|date|after:today',
            'is_confidential' => 'boolean',
        ]);

        // Handle file upload
        $file = $request->file('file');
        $fileName = time() . '_' . $file->getClientOriginalName();
        $filePath = $file->storeAs('client-documents/' . $client->id, $fileName, 'private');

        $documentData = [
            'name_ar' => $validated['name_ar'],
            'name_en' => $validated['name_en'],
            'type' => $validated['type'],
            'description_ar' => $validated['description_ar'],
            'description_en' => $validated['description_en'],
            'file_path' => $filePath,
            'file_name' => $fileName,
            'file_type' => $file->getClientMimeType(),
            'file_size' => $file->getSize(),
            'expiry_date' => $validated['expiry_date'] ?? null,
            'is_confidential' => $validated['is_confidential'] ?? false,
            'uploaded_by' => Auth::id(),
        ];

        $document = $client->documents()->create($documentData);

        return response()->json([
            'success' => true,
            'message' => 'تم رفع المستند بنجاح',
            'document' => $document
        ]);
    }

    /**
     * Download the specified document.
     */
    public function download(Client $client, ClientDocument $document): Response
    {
        $this->authorize('manageDocuments', $client);

        // Ensure the document belongs to this client
        if ($document->client_id !== $client->id) {
            abort(404);
        }

        if (!Storage::disk('private')->exists($document->file_path)) {
            abort(404, 'الملف غير موجود');
        }

        return Storage::disk('private')->download($document->file_path, $document->file_name);
    }

    /**
     * Remove the specified document from storage.
     */
    public function destroy(Client $client, ClientDocument $document): JsonResponse
    {
        $this->authorize('manageDocuments', $client);

        // Ensure the document belongs to this client
        if ($document->client_id !== $client->id) {
            abort(404);
        }

        $documentName = $document->name_ar;

        // Delete the file from storage
        if (Storage::disk('private')->exists($document->file_path)) {
            Storage::disk('private')->delete($document->file_path);
        }

        $document->delete();

        return response()->json([
            'success' => true,
            'message' => "تم حذف المستند {$documentName} بنجاح"
        ]);
    }
}
