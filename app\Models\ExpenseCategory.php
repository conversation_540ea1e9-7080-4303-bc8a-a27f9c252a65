<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExpenseCategory extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'code',
        'name_ar',
        'name_en',
        'description_ar',
        'description_en',
        'color',
        'icon',
        'parent_id',
        'is_active',
        'requires_approval',
        'approval_limit',
        'allowed_roles',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'requires_approval' => 'boolean',
        'approval_limit' => 'decimal:2',
        'allowed_roles' => 'array',
        'sort_order' => 'integer',
    ];

    /**
     * Get the parent category.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(ExpenseCategory::class, 'parent_id');
    }

    /**
     * Get the child categories.
     */
    public function children(): HasMany
    {
        return $this->hasMany(ExpenseCategory::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * Get all expenses in this category.
     */
    public function expenses(): HasMany
    {
        return $this->hasMany(Expense::class, 'category_id');
    }

    /**
     * Scope to get active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get root categories (no parent).
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Get the category's display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name_ar ?? $this->name_en ?? 'فئة بدون اسم';
    }

    /**
     * Get the category's full hierarchy path.
     */
    public function getHierarchyPathAttribute(): string
    {
        $path = [$this->display_name];
        $parent = $this->parent;
        
        while ($parent) {
            array_unshift($path, $parent->display_name);
            $parent = $parent->parent;
        }
        
        return implode(' > ', $path);
    }

    /**
     * Check if this category requires approval for the given amount.
     */
    public function requiresApprovalForAmount(float $amount): bool
    {
        if (!$this->requires_approval) {
            return false;
        }

        if ($this->approval_limit === null) {
            return true;
        }

        return $amount >= $this->approval_limit;
    }

    /**
     * Check if a user role is allowed to use this category.
     */
    public function isAllowedForRole(string $role): bool
    {
        if (empty($this->allowed_roles)) {
            return true;
        }

        return in_array($role, $this->allowed_roles);
    }

    /**
     * Get total expenses amount for this category.
     */
    public function getTotalExpensesAmountAttribute(): float
    {
        return $this->expenses()->sum('amount_sar');
    }

    /**
     * Get total expenses count for this category.
     */
    public function getTotalExpensesCountAttribute(): int
    {
        return $this->expenses()->count();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if (empty($category->code)) {
                $category->code = static::generateCode();
            }
        });
    }

    /**
     * Generate a unique category code.
     */
    private static function generateCode(): string
    {
        $prefix = 'EXP';
        $lastCategory = static::withTrashed()
            ->where('code', 'like', $prefix . '%')
            ->orderBy('code', 'desc')
            ->first();

        if ($lastCategory) {
            $lastNumber = (int) substr($lastCategory->code, strlen($prefix));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
