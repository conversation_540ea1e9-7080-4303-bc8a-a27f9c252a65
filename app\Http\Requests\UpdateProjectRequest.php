<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('update', $this->route('project'));
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Basic Information
            'name_ar' => ['required', 'string', 'max:255'],
            'name_en' => ['nullable', 'string', 'max:255'],
            'description_ar' => ['required', 'string', 'max:2000'],
            'description_en' => ['nullable', 'string', 'max:2000'],
            'client_id' => ['required', 'exists:clients,id'],
            'service_id' => ['nullable', 'exists:technical_services,id'],
            
            // Project Details
            'project_type' => ['required', 'in:website,mobile_app,web_app,ecommerce,custom_software,maintenance,consultation'],
            'category' => ['nullable', 'string', 'max:100'],
            'complexity_level' => ['required', 'in:simple,medium,complex,enterprise'],
            'priority' => ['required', 'in:low,medium,high,critical'],
            'status' => ['required', 'in:planning,active,on_hold,completed,cancelled'],
            
            // Timeline
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'actual_start_date' => ['nullable', 'date'],
            'actual_end_date' => ['nullable', 'date'],
            'estimated_hours' => ['nullable', 'integer', 'min:1'],
            'actual_hours' => ['nullable', 'integer', 'min:0'],
            
            // Financial
            'budget' => ['nullable', 'numeric', 'min:0'],
            'actual_cost' => ['nullable', 'numeric', 'min:0'],
            'hourly_rate' => ['nullable', 'numeric', 'min:0'],
            'currency' => ['nullable', 'string', 'max:3'],
            
            // Progress
            'progress_percentage' => ['nullable', 'integer', 'min:0', 'max:100'],
            
            // Management
            'project_manager_id' => ['nullable', 'exists:users,id'],
            'team_members' => ['nullable', 'array'],
            'team_members.*' => ['exists:users,id'],
            
            // Technical
            'technologies' => ['nullable', 'array'],
            'technologies.*' => ['string', 'max:50'],
            'requirements' => ['nullable', 'array'],
            'deliverables' => ['nullable', 'array'],
            'milestones' => ['nullable', 'array'],
            
            // Client Approval
            'client_approval_status' => ['nullable', 'in:pending,approved,rejected,changes_requested'],
            'client_feedback' => ['nullable', 'string', 'max:2000'],
            
            // Additional
            'notes_ar' => ['nullable', 'string', 'max:2000'],
            'notes_en' => ['nullable', 'string', 'max:2000'],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['string', 'max:50'],
            'custom_fields' => ['nullable', 'array'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name_ar.required' => 'اسم المشروع بالعربية مطلوب',
            'name_ar.max' => 'اسم المشروع لا يجب أن يتجاوز 255 حرف',
            'description_ar.required' => 'وصف المشروع مطلوب',
            'description_ar.max' => 'وصف المشروع لا يجب أن يتجاوز 2000 حرف',
            'client_id.required' => 'العميل مطلوب',
            'client_id.exists' => 'العميل المحدد غير موجود',
            'service_id.exists' => 'الخدمة المحددة غير موجودة',
            'project_type.required' => 'نوع المشروع مطلوب',
            'project_type.in' => 'نوع المشروع غير صحيح',
            'complexity_level.required' => 'مستوى التعقيد مطلوب',
            'complexity_level.in' => 'مستوى التعقيد غير صحيح',
            'priority.required' => 'أولوية المشروع مطلوبة',
            'priority.in' => 'أولوية المشروع غير صحيحة',
            'status.required' => 'حالة المشروع مطلوبة',
            'status.in' => 'حالة المشروع غير صحيحة',
            'start_date.required' => 'تاريخ البداية مطلوب',
            'start_date.date' => 'تاريخ البداية غير صحيح',
            'end_date.required' => 'تاريخ النهاية مطلوب',
            'end_date.date' => 'تاريخ النهاية غير صحيح',
            'end_date.after' => 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية',
            'actual_start_date.date' => 'تاريخ البداية الفعلي غير صحيح',
            'actual_end_date.date' => 'تاريخ النهاية الفعلي غير صحيح',
            'estimated_hours.integer' => 'الساعات المقدرة يجب أن تكون رقم صحيح',
            'estimated_hours.min' => 'الساعات المقدرة يجب أن تكون أكبر من صفر',
            'actual_hours.integer' => 'الساعات الفعلية يجب أن تكون رقم صحيح',
            'actual_hours.min' => 'الساعات الفعلية لا يمكن أن تكون سالبة',
            'budget.numeric' => 'الميزانية يجب أن تكون رقم',
            'budget.min' => 'الميزانية لا يمكن أن تكون سالبة',
            'actual_cost.numeric' => 'التكلفة الفعلية يجب أن تكون رقم',
            'actual_cost.min' => 'التكلفة الفعلية لا يمكن أن تكون سالبة',
            'hourly_rate.numeric' => 'السعر بالساعة يجب أن يكون رقم',
            'hourly_rate.min' => 'السعر بالساعة لا يمكن أن يكون سالب',
            'currency.max' => 'رمز العملة لا يجب أن يتجاوز 3 أحرف',
            'progress_percentage.integer' => 'نسبة التقدم يجب أن تكون رقم صحيح',
            'progress_percentage.min' => 'نسبة التقدم لا يمكن أن تكون سالبة',
            'progress_percentage.max' => 'نسبة التقدم لا يمكن أن تتجاوز 100%',
            'project_manager_id.exists' => 'مدير المشروع المحدد غير موجود',
            'team_members.array' => 'أعضاء الفريق يجب أن تكون مصفوفة',
            'team_members.*.exists' => 'أحد أعضاء الفريق المحددين غير موجود',
            'technologies.array' => 'التقنيات يجب أن تكون مصفوفة',
            'technologies.*.max' => 'كل تقنية لا يجب أن تتجاوز 50 حرف',
            'client_approval_status.in' => 'حالة موافقة العميل غير صحيحة',
            'client_feedback.max' => 'تعليقات العميل لا يجب أن تتجاوز 2000 حرف',
            'notes_ar.max' => 'الملاحظات لا يجب أن تتجاوز 2000 حرف',
            'notes_en.max' => 'الملاحظات بالإنجليزية لا يجب أن تتجاوز 2000 حرف',
            'tags.array' => 'العلامات يجب أن تكون مصفوفة',
            'tags.*.max' => 'كل علامة لا يجب أن تتجاوز 50 حرف',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name_ar' => 'اسم المشروع بالعربية',
            'name_en' => 'اسم المشروع بالإنجليزية',
            'description_ar' => 'وصف المشروع بالعربية',
            'description_en' => 'وصف المشروع بالإنجليزية',
            'client_id' => 'العميل',
            'service_id' => 'الخدمة',
            'project_type' => 'نوع المشروع',
            'category' => 'الفئة',
            'complexity_level' => 'مستوى التعقيد',
            'priority' => 'الأولوية',
            'status' => 'الحالة',
            'start_date' => 'تاريخ البداية',
            'end_date' => 'تاريخ النهاية',
            'actual_start_date' => 'تاريخ البداية الفعلي',
            'actual_end_date' => 'تاريخ النهاية الفعلي',
            'estimated_hours' => 'الساعات المقدرة',
            'actual_hours' => 'الساعات الفعلية',
            'budget' => 'الميزانية',
            'actual_cost' => 'التكلفة الفعلية',
            'hourly_rate' => 'السعر بالساعة',
            'currency' => 'العملة',
            'progress_percentage' => 'نسبة التقدم',
            'project_manager_id' => 'مدير المشروع',
            'team_members' => 'أعضاء الفريق',
            'technologies' => 'التقنيات',
            'requirements' => 'المتطلبات',
            'deliverables' => 'المخرجات',
            'milestones' => 'المعالم',
            'client_approval_status' => 'حالة موافقة العميل',
            'client_feedback' => 'تعليقات العميل',
            'notes_ar' => 'الملاحظات بالعربية',
            'notes_en' => 'الملاحظات بالإنجليزية',
            'tags' => 'العلامات',
            'custom_fields' => 'الحقول المخصصة',
        ];
    }
}
