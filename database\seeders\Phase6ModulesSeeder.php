<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Department;
use App\Models\ExpenseCategory;
use App\Models\ProductCategory;
use App\Models\Product;
use App\Models\User;

class Phase6ModulesSeeder extends Seeder
{
    /**
     * Run the database seeders.
     */
    public function run(): void
    {
        $this->seedDepartments();
        $this->seedExpenseCategories();
        $this->seedProductCategories();
        $this->seedSampleProducts();
    }

    /**
     * Seed departments.
     */
    private function seedDepartments(): void
    {
        $departments = [
            [
                'code' => 'MGMT',
                'name_ar' => 'الإدارة العليا',
                'name_en' => 'Management',
                'description_ar' => 'الإدارة العليا والقيادة التنفيذية',
                'color' => '#1f2937',
                'icon' => 'building-office',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'code' => 'DEV',
                'name_ar' => 'فريق التطوير',
                'name_en' => 'Development Team',
                'description_ar' => 'فريق تطوير البرمجيات والتطبيقات',
                'color' => '#3b82f6',
                'icon' => 'code-bracket',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'code' => 'DESIGN',
                'name_ar' => 'فريق التصميم',
                'name_en' => 'Design Team',
                'description_ar' => 'فريق التصميم الجرافيكي وتجربة المستخدم',
                'color' => '#8b5cf6',
                'icon' => 'paint-brush',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'code' => 'SALES',
                'name_ar' => 'فريق المبيعات',
                'name_en' => 'Sales Team',
                'description_ar' => 'فريق المبيعات وخدمة العملاء',
                'color' => '#10b981',
                'icon' => 'chart-bar',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'code' => 'MARKETING',
                'name_ar' => 'فريق التسويق',
                'name_en' => 'Marketing Team',
                'description_ar' => 'فريق التسويق الرقمي والإعلانات',
                'color' => '#f59e0b',
                'icon' => 'megaphone',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'code' => 'FINANCE',
                'name_ar' => 'فريق المحاسبة',
                'name_en' => 'Finance Team',
                'description_ar' => 'فريق المحاسبة والشؤون المالية',
                'color' => '#ef4444',
                'icon' => 'calculator',
                'is_active' => true,
                'sort_order' => 6,
            ],
        ];

        foreach ($departments as $department) {
            Department::firstOrCreate(
                ['code' => $department['code']],
                $department
            );
        }
    }

    /**
     * Seed expense categories.
     */
    private function seedExpenseCategories(): void
    {
        $categories = [
            [
                'code' => 'OFFICE',
                'name_ar' => 'مستلزمات المكتب',
                'name_en' => 'Office Supplies',
                'description_ar' => 'مستلزمات وأدوات المكتب',
                'color' => '#6b7280',
                'icon' => 'building-office',
                'requires_approval' => false,
                'approval_limit' => 1000.00,
                'sort_order' => 1,
            ],
            [
                'code' => 'SOFTWARE',
                'name_ar' => 'البرمجيات والتراخيص',
                'name_en' => 'Software & Licenses',
                'description_ar' => 'تراخيص البرمجيات والأدوات التقنية',
                'color' => '#3b82f6',
                'icon' => 'computer-desktop',
                'requires_approval' => true,
                'approval_limit' => 500.00,
                'sort_order' => 2,
            ],
            [
                'code' => 'HARDWARE',
                'name_ar' => 'الأجهزة والمعدات',
                'name_en' => 'Hardware & Equipment',
                'description_ar' => 'أجهزة الكمبيوتر والمعدات التقنية',
                'color' => '#8b5cf6',
                'icon' => 'cpu-chip',
                'requires_approval' => true,
                'approval_limit' => 2000.00,
                'sort_order' => 3,
            ],
            [
                'code' => 'MARKETING',
                'name_ar' => 'التسويق والإعلان',
                'name_en' => 'Marketing & Advertising',
                'description_ar' => 'مصاريف التسويق والحملات الإعلانية',
                'color' => '#f59e0b',
                'icon' => 'megaphone',
                'requires_approval' => true,
                'approval_limit' => 5000.00,
                'sort_order' => 4,
            ],
            [
                'code' => 'TRAVEL',
                'name_ar' => 'السفر والانتقال',
                'name_en' => 'Travel & Transportation',
                'description_ar' => 'مصاريف السفر والمواصلات',
                'color' => '#10b981',
                'icon' => 'truck',
                'requires_approval' => true,
                'approval_limit' => 3000.00,
                'sort_order' => 5,
            ],
            [
                'code' => 'UTILITIES',
                'name_ar' => 'المرافق والخدمات',
                'name_en' => 'Utilities & Services',
                'description_ar' => 'فواتير الكهرباء والماء والإنترنت',
                'color' => '#ef4444',
                'icon' => 'bolt',
                'requires_approval' => false,
                'approval_limit' => 2000.00,
                'sort_order' => 6,
            ],
            [
                'code' => 'TRAINING',
                'name_ar' => 'التدريب والتطوير',
                'name_en' => 'Training & Development',
                'description_ar' => 'دورات التدريب وتطوير المهارات',
                'color' => '#06b6d4',
                'icon' => 'academic-cap',
                'requires_approval' => true,
                'approval_limit' => 2000.00,
                'sort_order' => 7,
            ],
        ];

        foreach ($categories as $category) {
            ExpenseCategory::firstOrCreate(
                ['code' => $category['code']],
                $category
            );
        }
    }

    /**
     * Seed product categories.
     */
    private function seedProductCategories(): void
    {
        $categories = [
            [
                'code' => 'SERVICES',
                'name_ar' => 'الخدمات التقنية',
                'name_en' => 'Technical Services',
                'description_ar' => 'خدمات التطوير والتصميم',
                'color' => '#3b82f6',
                'icon' => 'cog',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'code' => 'PRODUCTS',
                'name_ar' => 'المنتجات الرقمية',
                'name_en' => 'Digital Products',
                'description_ar' => 'المنتجات والحلول الرقمية',
                'color' => '#8b5cf6',
                'icon' => 'cube',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'code' => 'HOSTING',
                'name_ar' => 'خدمات الاستضافة',
                'name_en' => 'Hosting Services',
                'description_ar' => 'خدمات الاستضافة والنطاقات',
                'color' => '#10b981',
                'icon' => 'server',
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($categories as $category) {
            ProductCategory::firstOrCreate(
                ['code' => $category['code']],
                $category
            );
        }
    }

    /**
     * Seed sample products.
     */
    private function seedSampleProducts(): void
    {
        $servicesCategory = ProductCategory::where('code', 'SERVICES')->first();
        $productsCategory = ProductCategory::where('code', 'PRODUCTS')->first();
        $hostingCategory = ProductCategory::where('code', 'HOSTING')->first();

        if (!$servicesCategory || !$productsCategory || !$hostingCategory) {
            return;
        }

        $products = [
            [
                'category_id' => $servicesCategory->id,
                'name_ar' => 'تطوير موقع إلكتروني',
                'name_en' => 'Website Development',
                'description_ar' => 'تطوير موقع إلكتروني متكامل',
                'type' => 'service',
                'cost_price' => 5000.00,
                'selling_price' => 8000.00,
                'track_inventory' => false,
                'unit_ar' => 'مشروع',
                'unit_en' => 'Project',
                'created_by' => 1,
            ],
            [
                'category_id' => $servicesCategory->id,
                'name_ar' => 'تطوير تطبيق جوال',
                'name_en' => 'Mobile App Development',
                'description_ar' => 'تطوير تطبيق جوال للأندرويد والآيفون',
                'type' => 'service',
                'cost_price' => 15000.00,
                'selling_price' => 25000.00,
                'track_inventory' => false,
                'unit_ar' => 'مشروع',
                'unit_en' => 'Project',
                'created_by' => 1,
            ],
            [
                'category_id' => $hostingCategory->id,
                'name_ar' => 'استضافة مشتركة',
                'name_en' => 'Shared Hosting',
                'description_ar' => 'استضافة مشتركة سنوية',
                'type' => 'service',
                'cost_price' => 200.00,
                'selling_price' => 400.00,
                'track_inventory' => true,
                'current_stock' => 100,
                'min_stock_level' => 10,
                'unit_ar' => 'سنة',
                'unit_en' => 'Year',
                'created_by' => 1,
            ],
        ];

        foreach ($products as $product) {
            Product::firstOrCreate(
                [
                    'name_ar' => $product['name_ar'],
                    'category_id' => $product['category_id']
                ],
                $product
            );
        }
    }
}
