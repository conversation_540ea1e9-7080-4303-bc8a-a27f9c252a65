<?php

namespace App\Policies;

use App\Models\Expense;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ExpensePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_expenses');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Expense $expense): bool
    {
        // Users can view their own expenses or if they have permission to view all expenses
        return $user->can('view_expenses') && 
               ($expense->user_id === $user->id || $user->can('approve_expenses'));
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_expenses');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Expense $expense): bool
    {
        // Users can edit their own expenses if they're in draft status, or if they have edit permission
        return $user->can('edit_expenses') && 
               (($expense->user_id === $user->id && $expense->status === 'draft') || 
                $user->can('approve_expenses'));
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Expense $expense): bool
    {
        // Users can delete their own expenses if they're in draft status, or if they have delete permission
        return $user->can('delete_expenses') && 
               (($expense->user_id === $user->id && $expense->status === 'draft') || 
                $user->can('approve_expenses'));
    }

    /**
     * Determine whether the user can approve the model.
     */
    public function approve(User $user, Expense $expense): bool
    {
        return $user->can('approve_expenses') && $expense->user_id !== $user->id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Expense $expense): bool
    {
        return $user->can('delete_expenses');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Expense $expense): bool
    {
        return $user->can('delete_expenses');
    }
}
