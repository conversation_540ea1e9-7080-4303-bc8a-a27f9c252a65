<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClientCommunication extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'type',
        'subject',
        'notes',
        'scheduled_at',
        'completed_at',
        'duration_minutes',
        'outcome',
        'outcome_notes',
        'next_follow_up_at',
        'attachments',
        'created_by',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'completed_at' => 'datetime',
        'next_follow_up_at' => 'datetime',
        'duration_minutes' => 'integer',
        'attachments' => 'array',
    ];

    /**
     * Get the client that owns the communication.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the user who created the communication.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the display type for the communication.
     */
    public function getDisplayTypeAttribute(): string
    {
        $types = [
            'call' => 'مكالمة هاتفية',
            'email' => 'بريد إلكتروني',
            'meeting' => 'اجتماع',
            'whatsapp' => 'واتساب',
            'sms' => 'رسالة نصية',
            'visit' => 'زيارة',
            'other' => 'أخرى',
        ];

        return $types[$this->type] ?? $this->type;
    }

    /**
     * Get the display outcome for the communication.
     */
    public function getDisplayOutcomeAttribute(): string
    {
        $outcomes = [
            'positive' => 'إيجابي',
            'neutral' => 'محايد',
            'negative' => 'سلبي',
            'follow_up_required' => 'يحتاج متابعة',
        ];

        return $outcomes[$this->outcome] ?? $this->outcome;
    }

    /**
     * Check if the communication is completed.
     */
    public function isCompleted(): bool
    {
        return !is_null($this->completed_at);
    }

    /**
     * Check if the communication is scheduled for the future.
     */
    public function isScheduled(): bool
    {
        return $this->scheduled_at && $this->scheduled_at->isFuture() && !$this->isCompleted();
    }

    /**
     * Check if the communication is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->scheduled_at && $this->scheduled_at->isPast() && !$this->isCompleted();
    }

    /**
     * Scope to get completed communications.
     */
    public function scopeCompleted($query)
    {
        return $query->whereNotNull('completed_at');
    }

    /**
     * Scope to get scheduled communications.
     */
    public function scopeScheduled($query)
    {
        return $query->whereNotNull('scheduled_at')
                    ->whereNull('completed_at')
                    ->where('scheduled_at', '>', now());
    }

    /**
     * Scope to get overdue communications.
     */
    public function scopeOverdue($query)
    {
        return $query->whereNotNull('scheduled_at')
                    ->whereNull('completed_at')
                    ->where('scheduled_at', '<', now());
    }

    /**
     * Scope to get communications by type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get communications by outcome.
     */
    public function scopeWithOutcome($query, $outcome)
    {
        return $query->where('outcome', $outcome);
    }
}
