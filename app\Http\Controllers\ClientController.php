<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreClientRequest;
use App\Http\Requests\UpdateClientRequest;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class ClientController extends Controller
{
    /**
     * Display a listing of clients.
     */
    public function index(Request $request): View
    {
        $this->authorize('viewAny', Client::class);

        return view('clients.index');
    }

    /**
     * Show the form for creating a new client.
     */
    public function create(): View
    {
        $this->authorize('create', Client::class);

        return view('clients.create');
    }

    /**
     * Store a newly created client in storage.
     */
    public function store(StoreClientRequest $request): RedirectResponse
    {
        $this->authorize('create', Client::class);

        $validated = $request->validated();
        $validated['created_by'] = Auth::id();

        $client = Client::create($validated);

        return redirect()
            ->route('clients.show', $client)
            ->with('success', __('تم إنشاء العميل بنجاح'));
    }

    /**
     * Display the specified client.
     */
    public function show(Client $client): View
    {
        $this->authorize('view', $client);

        $client->load([
            'contacts',
            'documents',
            'communications' => function ($query) {
                $query->latest()->limit(10);
            },
            'projects' => function ($query) {
                $query->latest()->limit(5);
            },
            'invoices' => function ($query) {
                $query->latest()->limit(5);
            },
            'assignedUser',
            'creator'
        ]);

        return view('clients.show', compact('client'));
    }

    /**
     * Show the form for editing the specified client.
     */
    public function edit(Client $client): View
    {
        $this->authorize('update', $client);

        return view('clients.edit', compact('client'));
    }

    /**
     * Update the specified client in storage.
     */
    public function update(UpdateClientRequest $request, Client $client): RedirectResponse
    {
        $this->authorize('update', $client);

        $validated = $request->validated();
        $validated['updated_by'] = Auth::id();

        $client->update($validated);

        return redirect()
            ->route('clients.show', $client)
            ->with('success', __('تم تحديث بيانات العميل بنجاح'));
    }

    /**
     * Remove the specified client from storage.
     */
    public function destroy(Client $client): RedirectResponse
    {
        $this->authorize('delete', $client);

        $clientName = $client->display_name;
        $client->delete();

        return redirect()
            ->route('clients.index')
            ->with('success', __('تم حذف العميل :name بنجاح', ['name' => $clientName]));
    }

    /**
     * Get clients data for API/AJAX requests.
     */
    public function getClientsData(Request $request)
    {
        $this->authorize('viewAny', Client::class);

        $query = Client::with(['assignedUser', 'creator'])
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name_ar', 'like', "%{$search}%")
                      ->orWhere('name_en', 'like', "%{$search}%")
                      ->orWhere('company_name_ar', 'like', "%{$search}%")
                      ->orWhere('company_name_en', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%")
                      ->orWhere('client_code', 'like', "%{$search}%");
                });
            })
            ->when($request->type, function ($query, $type) {
                $query->where('type', $type);
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->priority, function ($query, $priority) {
                $query->where('priority', $priority);
            })
            ->when($request->assigned_to, function ($query, $assignedTo) {
                $query->where('assigned_to', $assignedTo);
            });

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        
        $allowedSortFields = [
            'name_ar', 'name_en', 'email', 'phone', 'type', 
            'status', 'priority', 'created_at', 'last_contact_at'
        ];
        
        if (in_array($sortField, $allowedSortFields)) {
            $query->orderBy($sortField, $sortDirection);
        }

        $clients = $query->paginate($request->get('per_page', 15));

        return response()->json($clients);
    }
}
