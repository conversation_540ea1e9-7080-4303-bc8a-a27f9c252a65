<?php

namespace App\Policies;

use App\Models\Client;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ClientPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager', 'employee']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Client $client): bool
    {
        // Founders, admins, managers, and employees can view all clients
        if ($user->hasAnyRole(['founder', 'admin', 'manager', 'employee'])) {
            return true;
        }

        // Clients can only view their own data
        if ($user->hasRole('client')) {
            return $user->email === $client->email;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager', 'employee']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Client $client): bool
    {
        // Founders and admins can update any client
        if ($user->hasAnyRole(['founder', 'admin'])) {
            return true;
        }

        // Managers and employees can update clients assigned to them
        if ($user->hasAnyRole(['manager', 'employee'])) {
            return $client->assigned_to === $user->id || $client->created_by === $user->id;
        }

        // Clients can update their own basic information
        if ($user->hasRole('client')) {
            return $user->email === $client->email;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Client $client): bool
    {
        // Only founders and admins can delete clients
        return $user->hasAnyRole(['founder', 'admin']);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Client $client): bool
    {
        return $user->hasAnyRole(['founder', 'admin']);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Client $client): bool
    {
        return $user->hasRole('founder');
    }

    /**
     * Determine whether the user can manage client contacts.
     */
    public function manageContacts(User $user, Client $client): bool
    {
        // Founders, admins, managers, and employees can manage contacts
        if ($user->hasAnyRole(['founder', 'admin', 'manager', 'employee'])) {
            return true;
        }

        // Clients can manage their own contacts
        if ($user->hasRole('client')) {
            return $user->email === $client->email;
        }

        return false;
    }

    /**
     * Determine whether the user can manage client documents.
     */
    public function manageDocuments(User $user, Client $client): bool
    {
        // Founders, admins, managers, and employees can manage documents
        if ($user->hasAnyRole(['founder', 'admin', 'manager', 'employee'])) {
            return true;
        }

        // Clients can view their own documents but not upload new ones
        if ($user->hasRole('client')) {
            return $user->email === $client->email;
        }

        return false;
    }

    /**
     * Determine whether the user can upload documents for the client.
     */
    public function uploadDocuments(User $user, Client $client): bool
    {
        // Only internal users can upload documents
        return $user->hasAnyRole(['founder', 'admin', 'manager', 'employee']);
    }

    /**
     * Determine whether the user can view client communications.
     */
    public function viewCommunications(User $user, Client $client): bool
    {
        // Founders, admins, managers, and employees can view communications
        if ($user->hasAnyRole(['founder', 'admin', 'manager', 'employee'])) {
            return true;
        }

        // Clients can view their own communications
        if ($user->hasRole('client')) {
            return $user->email === $client->email;
        }

        return false;
    }

    /**
     * Determine whether the user can create communications for the client.
     */
    public function createCommunications(User $user, Client $client): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager', 'employee']);
    }

    /**
     * Determine whether the user can assign clients to other users.
     */
    public function assignClients(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can view client financial information.
     */
    public function viewFinancials(User $user, Client $client): bool
    {
        // Founders, admins, and managers can view financial information
        if ($user->hasAnyRole(['founder', 'admin', 'manager'])) {
            return true;
        }

        // Employees can view financials for assigned clients
        if ($user->hasRole('employee')) {
            return $client->assigned_to === $user->id;
        }

        // Clients can view their own financial information
        if ($user->hasRole('client')) {
            return $user->email === $client->email;
        }

        return false;
    }

    /**
     * Determine whether the user can export client data.
     */
    public function export(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }
}
