<?php

namespace App\Livewire\Projects;

use App\Models\Project;
use App\Models\Client;
use App\Models\User;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class ProjectForm extends Component
{
    public Project $project;
    public $isEditing = false;

    // Form Fields - Basic Information
    public $name_ar = '';
    public $name_en = '';
    public $description_ar = '';
    public $description_en = '';
    public $client_id = '';
    public $project_type = 'website';
    public $complexity_level = 'medium';
    public $priority = 'medium';
    public $status = 'planning';

    // Timeline
    public $start_date = '';
    public $end_date = '';
    public $estimated_hours = '';

    // Financial
    public $budget = '';
    public $hourly_rate = '';
    public $currency = 'SAR';

    // Management
    public $project_manager_id = '';
    public $team_members = [];

    // Technical
    public $technologies = [];
    public $requirements = [];
    public $deliverables = [];

    // Additional
    public $notes_ar = '';
    public $tags = [];

    // UI State
    public $currentStep = 1;
    public $totalSteps = 4;

    protected function rules()
    {
        $rules = [
            'name_ar' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'description_ar' => 'required|string|max:2000',
            'description_en' => 'nullable|string|max:2000',
            'client_id' => 'required|exists:clients,id',
            'project_type' => 'required|in:website,mobile_app,web_app,ecommerce,custom_software,maintenance,consultation',
            'complexity_level' => 'required|in:simple,medium,complex,enterprise',
            'priority' => 'required|in:low,medium,high,critical',
            'status' => 'required|in:planning,active,on_hold,completed,cancelled',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'estimated_hours' => 'nullable|integer|min:1',
            'budget' => 'nullable|numeric|min:0',
            'hourly_rate' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|max:3',
            'project_manager_id' => 'nullable|exists:users,id',
            'team_members' => 'nullable|array',
            'team_members.*' => 'exists:users,id',
            'technologies' => 'nullable|array',
            'requirements' => 'nullable|array',
            'deliverables' => 'nullable|array',
            'notes_ar' => 'nullable|string|max:2000',
            'tags' => 'nullable|array',
        ];

        return $rules;
    }

    protected $messages = [
        'name_ar.required' => 'اسم المشروع بالعربية مطلوب',
        'description_ar.required' => 'وصف المشروع مطلوب',
        'client_id.required' => 'العميل مطلوب',
        'client_id.exists' => 'العميل المحدد غير موجود',
        'project_type.required' => 'نوع المشروع مطلوب',
        'start_date.required' => 'تاريخ البداية مطلوب',
        'end_date.required' => 'تاريخ النهاية مطلوب',
        'end_date.after' => 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية',
    ];

    public function mount(Project $project = null)
    {
        if ($project && $project->exists) {
            $this->isEditing = true;
            $this->project = $project;
            $this->authorize('update', $project);
            $this->fillForm();
        } else {
            $this->authorize('create', Project::class);
            $this->project = new Project();
        }
    }

    public function fillForm()
    {
        $this->name_ar = $this->project->name_ar;
        $this->name_en = $this->project->name_en;
        $this->description_ar = $this->project->description_ar;
        $this->description_en = $this->project->description_en;
        $this->client_id = $this->project->client_id;
        $this->project_type = $this->project->project_type;
        $this->complexity_level = $this->project->complexity_level;
        $this->priority = $this->project->priority;
        $this->status = $this->project->status;
        $this->start_date = $this->project->start_date?->format('Y-m-d');
        $this->end_date = $this->project->end_date?->format('Y-m-d');
        $this->estimated_hours = $this->project->estimated_hours;
        $this->budget = $this->project->budget;
        $this->hourly_rate = $this->project->hourly_rate;
        $this->currency = $this->project->currency ?? 'SAR';
        $this->project_manager_id = $this->project->project_manager_id;
        $this->team_members = $this->project->team_members ?? [];
        $this->technologies = $this->project->technologies ?? [];
        $this->requirements = $this->project->requirements ?? [];
        $this->deliverables = $this->project->deliverables ?? [];
        $this->notes_ar = $this->project->notes_ar;
        $this->tags = $this->project->tags ?? [];
    }

    public function nextStep()
    {
        $this->validateCurrentStep();
        if ($this->currentStep < $this->totalSteps) {
            $this->currentStep++;
        }
    }

    public function previousStep()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }
    }

    public function goToStep($step)
    {
        if ($step >= 1 && $step <= $this->totalSteps) {
            $this->currentStep = $step;
        }
    }

    private function validateCurrentStep()
    {
        $rules = [];
        
        switch ($this->currentStep) {
            case 1: // Basic Information
                $rules = [
                    'name_ar' => 'required|string|max:255',
                    'description_ar' => 'required|string|max:2000',
                    'client_id' => 'required|exists:clients,id',
                    'project_type' => 'required',
                ];
                break;
            case 2: // Timeline & Management
                $rules = [
                    'start_date' => 'required|date',
                    'end_date' => 'required|date|after:start_date',
                    'complexity_level' => 'required',
                    'priority' => 'required',
                ];
                break;
            case 3: // Financial & Team
                $rules = [
                    'budget' => 'nullable|numeric|min:0',
                    'hourly_rate' => 'nullable|numeric|min:0',
                ];
                break;
            case 4: // Technical & Additional
                $rules = [
                    'notes_ar' => 'nullable|string|max:2000',
                ];
                break;
        }

        $this->validate($rules);
    }

    public function addTechnology()
    {
        $this->technologies[] = '';
    }

    public function removeTechnology($index)
    {
        unset($this->technologies[$index]);
        $this->technologies = array_values($this->technologies);
    }

    public function addRequirement()
    {
        $this->requirements[] = '';
    }

    public function removeRequirement($index)
    {
        unset($this->requirements[$index]);
        $this->requirements = array_values($this->requirements);
    }

    public function addDeliverable()
    {
        $this->deliverables[] = '';
    }

    public function removeDeliverable($index)
    {
        unset($this->deliverables[$index]);
        $this->deliverables = array_values($this->deliverables);
    }

    public function save()
    {
        $this->validate();

        $data = [
            'name_ar' => $this->name_ar,
            'name_en' => $this->name_en,
            'description_ar' => $this->description_ar,
            'description_en' => $this->description_en,
            'client_id' => $this->client_id,
            'project_type' => $this->project_type,
            'complexity_level' => $this->complexity_level,
            'priority' => $this->priority,
            'status' => $this->status,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'estimated_hours' => $this->estimated_hours ?: null,
            'budget' => $this->budget ?: null,
            'hourly_rate' => $this->hourly_rate ?: null,
            'currency' => $this->currency,
            'project_manager_id' => $this->project_manager_id ?: null,
            'team_members' => array_filter($this->team_members),
            'technologies' => array_filter($this->technologies),
            'requirements' => array_filter($this->requirements),
            'deliverables' => array_filter($this->deliverables),
            'notes_ar' => $this->notes_ar,
            'tags' => array_filter($this->tags),
        ];

        if ($this->isEditing) {
            $data['updated_by'] = Auth::id();
            $this->project->update($data);
            $message = 'تم تحديث بيانات المشروع بنجاح';
        } else {
            $data['created_by'] = Auth::id();
            $this->project = Project::create($data);
            $message = 'تم إنشاء المشروع بنجاح';
        }

        session()->flash('success', $message);
        return redirect()->route('projects.show', $this->project);
    }

    public function getClientsProperty()
    {
        return Client::orderBy('name_ar')->get();
    }

    public function getUsersProperty()
    {
        return User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['founder', 'admin', 'manager', 'employee']);
        })->orderBy('name')->get();
    }

    public function render()
    {
        return view('livewire.projects.project-form', [
            'clients' => $this->clients,
            'users' => $this->users,
        ]);
    }
}
