<?php

namespace App\Livewire\Inventory;

use App\Models\Product;
use App\Models\ProductCategory;
use Livewire\Component;
use Livewire\WithPagination;

class ProductManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $categoryFilter = '';
    public $statusFilter = '';
    public $typeFilter = '';
    public $showModal = false;
    public $editingProduct = null;

    // Form fields
    public $name_ar = '';
    public $name_en = '';
    public $description_ar = '';
    public $category_id = '';
    public $type = 'product';
    public $cost_price = 0;
    public $selling_price = 0;
    public $current_stock = 0;
    public $min_stock_level = 0;
    public $track_inventory = true;
    public $unit_ar = 'قطعة';
    public $unit_en = 'Piece';
    public $status = 'active';

    protected $rules = [
        'name_ar' => 'required|string|max:255',
        'name_en' => 'nullable|string|max:255',
        'description_ar' => 'nullable|string',
        'category_id' => 'required|exists:product_categories,id',
        'type' => 'required|in:product,service,digital,bundle',
        'cost_price' => 'required|numeric|min:0',
        'selling_price' => 'required|numeric|min:0',
        'current_stock' => 'required_if:track_inventory,true|integer|min:0',
        'min_stock_level' => 'required_if:track_inventory,true|integer|min:0',
        'track_inventory' => 'boolean',
        'unit_ar' => 'required|string|max:50',
        'status' => 'required|in:active,inactive,discontinued,out_of_stock',
    ];

    protected $messages = [
        'name_ar.required' => 'اسم المنتج باللغة العربية مطلوب',
        'category_id.required' => 'فئة المنتج مطلوبة',
        'category_id.exists' => 'الفئة المحددة غير موجودة',
        'cost_price.required' => 'سعر التكلفة مطلوب',
        'selling_price.required' => 'سعر البيع مطلوب',
        'current_stock.required_if' => 'المخزون الحالي مطلوب عند تفعيل تتبع المخزون',
        'min_stock_level.required_if' => 'الحد الأدنى للمخزون مطلوب عند تفعيل تتبع المخزون',
    ];

    public function mount()
    {
        $this->authorize('viewAny', Product::class);
    }

    public function render()
    {
        $products = Product::with(['category'])
            ->when($this->search, function ($query) {
                $query->where('name_ar', 'like', '%' . $this->search . '%')
                      ->orWhere('name_en', 'like', '%' . $this->search . '%')
                      ->orWhere('sku', 'like', '%' . $this->search . '%');
            })
            ->when($this->categoryFilter, function ($query) {
                $query->where('category_id', $this->categoryFilter);
            })
            ->when($this->statusFilter, function ($query) {
                $query->where('status', $this->statusFilter);
            })
            ->when($this->typeFilter, function ($query) {
                $query->where('type', $this->typeFilter);
            })
            ->orderBy('name_ar')
            ->paginate(15);

        $categories = ProductCategory::active()->orderBy('name_ar')->get();

        return view('livewire.inventory.product-management', [
            'products' => $products,
            'categories' => $categories,
        ]);
    }

    public function openCreateModal()
    {
        $this->authorize('create', Product::class);
        $this->resetForm();
        $this->showModal = true;
    }

    public function openEditModal(Product $product)
    {
        $this->authorize('update', $product);
        $this->editingProduct = $product;
        $this->fillForm($product);
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    public function save()
    {
        $this->validate();

        $data = [
            'name_ar' => $this->name_ar,
            'name_en' => $this->name_en,
            'description_ar' => $this->description_ar,
            'category_id' => $this->category_id,
            'type' => $this->type,
            'cost_price' => $this->cost_price,
            'selling_price' => $this->selling_price,
            'track_inventory' => $this->track_inventory,
            'unit_ar' => $this->unit_ar,
            'unit_en' => $this->unit_en,
            'status' => $this->status,
        ];

        if ($this->track_inventory) {
            $data['current_stock'] = $this->current_stock;
            $data['min_stock_level'] = $this->min_stock_level;
        }

        if ($this->editingProduct) {
            $this->authorize('update', $this->editingProduct);
            $this->editingProduct->update($data);
            $message = 'تم تحديث المنتج بنجاح';
        } else {
            $this->authorize('create', Product::class);
            $data['created_by'] = auth()->id();
            Product::create($data);
            $message = 'تم إنشاء المنتج بنجاح';
        }

        $this->closeModal();
        session()->flash('message', $message);
    }

    public function deleteProduct(Product $product)
    {
        $this->authorize('delete', $product);
        $product->delete();
        session()->flash('message', 'تم حذف المنتج بنجاح');
    }

    public function toggleStatus(Product $product)
    {
        $this->authorize('update', $product);
        $newStatus = $product->status === 'active' ? 'inactive' : 'active';
        $product->update(['status' => $newStatus]);
        
        $statusText = $newStatus === 'active' ? 'تفعيل' : 'إلغاء تفعيل';
        session()->flash('message', "تم {$statusText} المنتج بنجاح");
    }

    private function resetForm()
    {
        $this->editingProduct = null;
        $this->name_ar = '';
        $this->name_en = '';
        $this->description_ar = '';
        $this->category_id = '';
        $this->type = 'product';
        $this->cost_price = 0;
        $this->selling_price = 0;
        $this->current_stock = 0;
        $this->min_stock_level = 0;
        $this->track_inventory = true;
        $this->unit_ar = 'قطعة';
        $this->unit_en = 'Piece';
        $this->status = 'active';
        $this->resetErrorBag();
    }

    private function fillForm(Product $product)
    {
        $this->name_ar = $product->name_ar;
        $this->name_en = $product->name_en;
        $this->description_ar = $product->description_ar;
        $this->category_id = $product->category_id;
        $this->type = $product->type;
        $this->cost_price = $product->cost_price;
        $this->selling_price = $product->selling_price;
        $this->current_stock = $product->current_stock;
        $this->min_stock_level = $product->min_stock_level;
        $this->track_inventory = $product->track_inventory;
        $this->unit_ar = $product->unit_ar;
        $this->unit_en = $product->unit_en;
        $this->status = $product->status;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingCategoryFilter()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingTypeFilter()
    {
        $this->resetPage();
    }
}
