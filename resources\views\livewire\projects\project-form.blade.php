<div>
    <form wire:submit="save">
        <!-- Progress Steps -->
        <div class="px-6 py-4 border-b border-secondary-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-secondary-900">
                    {{ $isEditing ? 'تعديل بيانات المشروع' : 'إضافة مشروع جديد' }}
                </h3>
                <div class="flex items-center space-x-2 space-x-reverse">
                    @for($i = 1; $i <= $totalSteps; $i++)
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium {{ $currentStep >= $i ? 'bg-primary-600 text-white' : 'bg-secondary-200 text-secondary-600' }}">
                                {{ $i }}
                            </div>
                            @if($i < $totalSteps)
                                <div class="w-8 h-0.5 {{ $currentStep > $i ? 'bg-primary-600' : 'bg-secondary-200' }}"></div>
                            @endif
                        </div>
                    @endfor
                </div>
            </div>
            
            <!-- Step Labels -->
            <div class="mt-2 flex justify-between text-xs text-secondary-500">
                <span class="{{ $currentStep === 1 ? 'text-primary-600 font-medium' : '' }}">المعلومات الأساسية</span>
                <span class="{{ $currentStep === 2 ? 'text-primary-600 font-medium' : '' }}">الجدولة والإدارة</span>
                <span class="{{ $currentStep === 3 ? 'text-primary-600 font-medium' : '' }}">المالية والفريق</span>
                <span class="{{ $currentStep === 4 ? 'text-primary-600 font-medium' : '' }}">التقنية والإضافات</span>
            </div>
        </div>

        <div class="p-6">
            <!-- Step 1: Basic Information -->
            @if($currentStep === 1)
                <div class="space-y-6">
                    <h4 class="text-md font-medium text-secondary-900 mb-4">المعلومات الأساسية</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">اسم المشروع بالعربية *</label>
                            <input wire:model="name_ar" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('name_ar') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">اسم المشروع بالإنجليزية</label>
                            <input wire:model="name_en" type="text" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('name_en') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">العميل *</label>
                        <select wire:model="client_id" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">اختر العميل</option>
                            @foreach($clients as $client)
                                <option value="{{ $client->id }}">{{ $client->display_name }}</option>
                            @endforeach
                        </select>
                        @error('client_id') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">نوع المشروع *</label>
                            <select wire:model="project_type" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                <option value="website">موقع ويب</option>
                                <option value="mobile_app">تطبيق جوال</option>
                                <option value="web_app">تطبيق ويب</option>
                                <option value="ecommerce">متجر إلكتروني</option>
                                <option value="custom_software">برمجيات مخصصة</option>
                                <option value="maintenance">صيانة</option>
                                <option value="consultation">استشارة</option>
                            </select>
                            @error('project_type') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">مستوى التعقيد *</label>
                            <select wire:model="complexity_level" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                <option value="simple">بسيط</option>
                                <option value="medium">متوسط</option>
                                <option value="complex">معقد</option>
                                <option value="enterprise">مؤسسي</option>
                            </select>
                            @error('complexity_level') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">وصف المشروع *</label>
                        <textarea wire:model="description_ar" rows="4" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"></textarea>
                        @error('description_ar') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">وصف المشروع بالإنجليزية</label>
                        <textarea wire:model="description_en" rows="3" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"></textarea>
                        @error('description_en') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                </div>
            @endif

            <!-- Step 2: Timeline & Management -->
            @if($currentStep === 2)
                <div class="space-y-6">
                    <h4 class="text-md font-medium text-secondary-900 mb-4">الجدولة والإدارة</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">تاريخ البداية *</label>
                            <input wire:model="start_date" type="date" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('start_date') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">تاريخ النهاية *</label>
                            <input wire:model="end_date" type="date" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('end_date') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">الأولوية *</label>
                            <select wire:model="priority" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                <option value="low">منخفضة</option>
                                <option value="medium">متوسطة</option>
                                <option value="high">عالية</option>
                                <option value="critical">حرجة</option>
                            </select>
                            @error('priority') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">الحالة *</label>
                            <select wire:model="status" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                <option value="planning">قيد التخطيط</option>
                                <option value="active">نشط</option>
                                <option value="on_hold">معلق</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                            @error('status') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">الساعات المقدرة</label>
                        <input wire:model="estimated_hours" type="number" min="1" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        @error('estimated_hours') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">مدير المشروع</label>
                        <select wire:model="project_manager_id" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">اختر مدير المشروع</option>
                            @foreach($users as $user)
                                <option value="{{ $user->id }}">{{ $user->name }}</option>
                            @endforeach
                        </select>
                        @error('project_manager_id') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                </div>
            @endif

            <!-- Step 3: Financial & Team -->
            @if($currentStep === 3)
                <div class="space-y-6">
                    <h4 class="text-md font-medium text-secondary-900 mb-4">المالية والفريق</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">الميزانية</label>
                            <input wire:model="budget" type="number" min="0" step="0.01" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('budget') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">السعر بالساعة</label>
                            <input wire:model="hourly_rate" type="number" min="0" step="0.01" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            @error('hourly_rate') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary-700 mb-1">العملة</label>
                            <select wire:model="currency" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                                <option value="SAR">ريال سعودي</option>
                                <option value="USD">دولار أمريكي</option>
                                <option value="EUR">يورو</option>
                            </select>
                            @error('currency') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">أعضاء الفريق</label>
                        <select wire:model="team_members" multiple class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500" size="5">
                            @foreach($users as $user)
                                <option value="{{ $user->id }}">{{ $user->name }}</option>
                            @endforeach
                        </select>
                        <p class="mt-1 text-sm text-secondary-500">اضغط Ctrl (أو Cmd) لتحديد عدة أعضاء</p>
                        @error('team_members') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                </div>
            @endif

            <!-- Step 4: Technical & Additional -->
            @if($currentStep === 4)
                <div class="space-y-6">
                    <h4 class="text-md font-medium text-secondary-900 mb-4">التقنية والإضافات</h4>
                    
                    <!-- Technologies -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">التقنيات المستخدمة</label>
                        @foreach($technologies as $index => $technology)
                            <div class="flex items-center space-x-2 space-x-reverse mb-2">
                                <input wire:model="technologies.{{ $index }}" type="text" class="flex-1 px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500" placeholder="اسم التقنية">
                                <button type="button" wire:click="removeTechnology({{ $index }})" class="text-red-600 hover:text-red-800">
                                    <x-icon name="trash" class="w-4 h-4" />
                                </button>
                            </div>
                        @endforeach
                        <button type="button" wire:click="addTechnology" class="text-primary-600 hover:text-primary-800 text-sm">
                            + إضافة تقنية
                        </button>
                    </div>

                    <!-- Requirements -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">المتطلبات</label>
                        @foreach($requirements as $index => $requirement)
                            <div class="flex items-center space-x-2 space-x-reverse mb-2">
                                <input wire:model="requirements.{{ $index }}" type="text" class="flex-1 px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500" placeholder="وصف المتطلب">
                                <button type="button" wire:click="removeRequirement({{ $index }})" class="text-red-600 hover:text-red-800">
                                    <x-icon name="trash" class="w-4 h-4" />
                                </button>
                            </div>
                        @endforeach
                        <button type="button" wire:click="addRequirement" class="text-primary-600 hover:text-primary-800 text-sm">
                            + إضافة متطلب
                        </button>
                    </div>

                    <!-- Deliverables -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">المخرجات</label>
                        @foreach($deliverables as $index => $deliverable)
                            <div class="flex items-center space-x-2 space-x-reverse mb-2">
                                <input wire:model="deliverables.{{ $index }}" type="text" class="flex-1 px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500" placeholder="وصف المخرج">
                                <button type="button" wire:click="removeDeliverable({{ $index }})" class="text-red-600 hover:text-red-800">
                                    <x-icon name="trash" class="w-4 h-4" />
                                </button>
                            </div>
                        @endforeach
                        <button type="button" wire:click="addDeliverable" class="text-primary-600 hover:text-primary-800 text-sm">
                            + إضافة مخرج
                        </button>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">ملاحظات إضافية</label>
                        <textarea wire:model="notes_ar" rows="4" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"></textarea>
                        @error('notes_ar') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                </div>
            @endif
        </div>

        <!-- Form Actions -->
        <div class="px-6 py-4 bg-secondary-50 border-t border-secondary-200 flex items-center justify-between">
            <div class="flex items-center space-x-3 space-x-reverse">
                @if($currentStep > 1)
                    <button type="button" wire:click="previousStep" class="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        السابق
                    </button>
                @endif
            </div>

            <div class="flex items-center space-x-3 space-x-reverse">
                <a href="{{ $isEditing ? route('projects.show', $project) : route('projects.index') }}" 
                   class="bg-secondary-300 hover:bg-secondary-400 text-secondary-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                    إلغاء
                </a>
                
                @if($currentStep < $totalSteps)
                    <button type="button" wire:click="nextStep" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        التالي
                    </button>
                @else
                    <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        {{ $isEditing ? 'تحديث المشروع' : 'إنشاء المشروع' }}
                    </button>
                @endif
            </div>
        </div>
    </form>
</div>
