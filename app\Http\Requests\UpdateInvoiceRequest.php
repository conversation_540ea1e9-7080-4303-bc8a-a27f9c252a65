<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateInvoiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('update', $this->route('invoice'));
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Basic Information
            'type' => ['required', 'in:invoice,quote,proforma,credit_note,debit_note'],
            'client_id' => ['required', 'exists:clients,id'],
            'project_id' => ['nullable', 'exists:projects,id'],
            'subject_ar' => ['required', 'string', 'max:255'],
            'subject_en' => ['nullable', 'string', 'max:255'],
            'description_ar' => ['nullable', 'string', 'max:2000'],
            'description_en' => ['nullable', 'string', 'max:2000'],
            
            // Dates
            'issue_date' => ['required', 'date'],
            'due_date' => ['required', 'date', 'after_or_equal:issue_date'],
            
            // Financial
            'currency' => ['required', 'string', 'max:3'],
            'discount_rate' => ['nullable', 'numeric', 'min:0', 'max:100'],
            'tax_rate' => ['nullable', 'numeric', 'min:0', 'max:100'],
            
            // Terms and Notes
            'terms_ar' => ['nullable', 'string', 'max:2000'],
            'terms_en' => ['nullable', 'string', 'max:2000'],
            'notes_ar' => ['nullable', 'string', 'max:2000'],
            'notes_en' => ['nullable', 'string', 'max:2000'],
            
            // Status
            'status' => ['required', 'in:draft,sent,viewed,approved,paid,partially_paid,overdue,cancelled,refunded'],
            
            // Recurring
            'is_recurring' => ['boolean'],
            'recurring_settings' => ['nullable', 'array'],
            'recurring_settings.frequency' => ['required_if:is_recurring,true', 'in:weekly,monthly,quarterly,yearly'],
            'recurring_settings.interval' => ['required_if:is_recurring,true', 'integer', 'min:1', 'max:12'],
            'recurring_settings.end_date' => ['nullable', 'date', 'after:due_date'],
            'recurring_settings.max_occurrences' => ['nullable', 'integer', 'min:1', 'max:100'],
            
            // Custom Fields
            'custom_fields' => ['nullable', 'array'],
            
            // Invoice Items
            'items' => ['required', 'array', 'min:1'],
            'items.*.service_id' => ['nullable', 'exists:technical_services,id'],
            'items.*.item_name_ar' => ['required', 'string', 'max:255'],
            'items.*.item_name_en' => ['nullable', 'string', 'max:255'],
            'items.*.description_ar' => ['nullable', 'string', 'max:1000'],
            'items.*.description_en' => ['nullable', 'string', 'max:1000'],
            'items.*.quantity' => ['required', 'numeric', 'min:0.01'],
            'items.*.unit_ar' => ['required', 'string', 'max:50'],
            'items.*.unit_en' => ['nullable', 'string', 'max:50'],
            'items.*.unit_price' => ['required', 'numeric', 'min:0'],
            'items.*.discount_rate' => ['nullable', 'numeric', 'min:0', 'max:100'],
            'items.*.tax_rate' => ['nullable', 'numeric', 'min:0', 'max:100'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'type.required' => 'نوع الفاتورة مطلوب',
            'type.in' => 'نوع الفاتورة غير صحيح',
            'client_id.required' => 'العميل مطلوب',
            'client_id.exists' => 'العميل المحدد غير موجود',
            'project_id.exists' => 'المشروع المحدد غير موجود',
            'subject_ar.required' => 'موضوع الفاتورة مطلوب',
            'subject_ar.max' => 'موضوع الفاتورة لا يجب أن يتجاوز 255 حرف',
            'description_ar.max' => 'وصف الفاتورة لا يجب أن يتجاوز 2000 حرف',
            'issue_date.required' => 'تاريخ الإصدار مطلوب',
            'issue_date.date' => 'تاريخ الإصدار غير صحيح',
            'due_date.required' => 'تاريخ الاستحقاق مطلوب',
            'due_date.date' => 'تاريخ الاستحقاق غير صحيح',
            'due_date.after_or_equal' => 'تاريخ الاستحقاق يجب أن يكون بعد أو يساوي تاريخ الإصدار',
            'currency.required' => 'العملة مطلوبة',
            'currency.max' => 'رمز العملة لا يجب أن يتجاوز 3 أحرف',
            'discount_rate.numeric' => 'نسبة الخصم يجب أن تكون رقم',
            'discount_rate.min' => 'نسبة الخصم لا يمكن أن تكون سالبة',
            'discount_rate.max' => 'نسبة الخصم لا يمكن أن تتجاوز 100%',
            'tax_rate.numeric' => 'نسبة الضريبة يجب أن تكون رقم',
            'tax_rate.min' => 'نسبة الضريبة لا يمكن أن تكون سالبة',
            'tax_rate.max' => 'نسبة الضريبة لا يمكن أن تتجاوز 100%',
            'terms_ar.max' => 'الشروط والأحكام لا يجب أن تتجاوز 2000 حرف',
            'notes_ar.max' => 'الملاحظات لا يجب أن تتجاوز 2000 حرف',
            'status.required' => 'حالة الفاتورة مطلوبة',
            'status.in' => 'حالة الفاتورة غير صحيحة',
            'items.required' => 'عناصر الفاتورة مطلوبة',
            'items.array' => 'عناصر الفاتورة يجب أن تكون مصفوفة',
            'items.min' => 'يجب إضافة عنصر واحد على الأقل',
            'items.*.service_id.exists' => 'الخدمة المحددة غير موجودة',
            'items.*.item_name_ar.required' => 'اسم العنصر مطلوب',
            'items.*.item_name_ar.max' => 'اسم العنصر لا يجب أن يتجاوز 255 حرف',
            'items.*.quantity.required' => 'الكمية مطلوبة',
            'items.*.quantity.numeric' => 'الكمية يجب أن تكون رقم',
            'items.*.quantity.min' => 'الكمية يجب أن تكون أكبر من صفر',
            'items.*.unit_ar.required' => 'الوحدة مطلوبة',
            'items.*.unit_ar.max' => 'الوحدة لا يجب أن تتجاوز 50 حرف',
            'items.*.unit_price.required' => 'سعر الوحدة مطلوب',
            'items.*.unit_price.numeric' => 'سعر الوحدة يجب أن يكون رقم',
            'items.*.unit_price.min' => 'سعر الوحدة لا يمكن أن يكون سالب',
            'items.*.discount_rate.numeric' => 'نسبة الخصم يجب أن تكون رقم',
            'items.*.discount_rate.min' => 'نسبة الخصم لا يمكن أن تكون سالبة',
            'items.*.discount_rate.max' => 'نسبة الخصم لا يمكن أن تتجاوز 100%',
            'items.*.tax_rate.numeric' => 'نسبة الضريبة يجب أن تكون رقم',
            'items.*.tax_rate.min' => 'نسبة الضريبة لا يمكن أن تكون سالبة',
            'items.*.tax_rate.max' => 'نسبة الضريبة لا يمكن أن تتجاوز 100%',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'type' => 'نوع الفاتورة',
            'client_id' => 'العميل',
            'project_id' => 'المشروع',
            'subject_ar' => 'موضوع الفاتورة',
            'subject_en' => 'موضوع الفاتورة بالإنجليزية',
            'description_ar' => 'وصف الفاتورة',
            'description_en' => 'وصف الفاتورة بالإنجليزية',
            'issue_date' => 'تاريخ الإصدار',
            'due_date' => 'تاريخ الاستحقاق',
            'currency' => 'العملة',
            'discount_rate' => 'نسبة الخصم',
            'tax_rate' => 'نسبة الضريبة',
            'terms_ar' => 'الشروط والأحكام',
            'terms_en' => 'الشروط والأحكام بالإنجليزية',
            'notes_ar' => 'الملاحظات',
            'notes_en' => 'الملاحظات بالإنجليزية',
            'status' => 'الحالة',
            'is_recurring' => 'فاتورة متكررة',
            'items' => 'عناصر الفاتورة',
        ];
    }
}
