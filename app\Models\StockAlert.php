<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StockAlert extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'type',
        'status',
        'message_ar',
        'message_en',
        'current_stock',
        'threshold_level',
        'expiry_date',
        'priority',
        'acknowledged_by',
        'acknowledged_at',
        'acknowledgment_notes',
        'resolved_by',
        'resolved_at',
        'resolution_notes',
    ];

    protected $casts = [
        'current_stock' => 'integer',
        'threshold_level' => 'integer',
        'expiry_date' => 'date',
        'acknowledged_at' => 'datetime',
        'resolved_at' => 'datetime',
    ];

    /**
     * Get the product this alert belongs to.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the user who acknowledged this alert.
     */
    public function acknowledger(): BelongsTo
    {
        return $this->belongsTo(User::class, 'acknowledged_by');
    }

    /**
     * Get the user who resolved this alert.
     */
    public function resolver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    /**
     * Scope to filter by type.
     */
    public function scopeType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by priority.
     */
    public function scopePriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to get active alerts.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get critical alerts.
     */
    public function scopeCritical($query)
    {
        return $query->where('priority', 'critical');
    }

    /**
     * Scope to get high priority alerts.
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['critical', 'high']);
    }

    /**
     * Get the alert type display name.
     */
    public function getTypeDisplayAttribute(): string
    {
        return match($this->type) {
            'low_stock' => 'مخزون منخفض',
            'out_of_stock' => 'نفاد المخزون',
            'overstock' => 'مخزون زائد',
            'expiry_warning' => 'تحذير انتهاء الصلاحية',
            default => $this->type,
        };
    }

    /**
     * Get the alert priority display name.
     */
    public function getPriorityDisplayAttribute(): string
    {
        return match($this->priority) {
            'low' => 'منخفض',
            'medium' => 'متوسط',
            'high' => 'عالي',
            'critical' => 'حرج',
            default => $this->priority,
        };
    }

    /**
     * Get the alert status display name.
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'active' => 'نشط',
            'acknowledged' => 'تم الإقرار',
            'resolved' => 'تم الحل',
            default => $this->status,
        };
    }

    /**
     * Get the alert message.
     */
    public function getDisplayMessageAttribute(): string
    {
        return $this->message_ar ?? $this->message_en ?? 'تنبيه بدون رسالة';
    }

    /**
     * Get the priority color class.
     */
    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'low' => 'text-blue-600 bg-blue-100',
            'medium' => 'text-yellow-600 bg-yellow-100',
            'high' => 'text-orange-600 bg-orange-100',
            'critical' => 'text-red-600 bg-red-100',
            default => 'text-gray-600 bg-gray-100',
        };
    }

    /**
     * Check if the alert can be acknowledged.
     */
    public function canBeAcknowledged(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the alert can be resolved.
     */
    public function canBeResolved(): bool
    {
        return in_array($this->status, ['active', 'acknowledged']);
    }

    /**
     * Acknowledge the alert.
     */
    public function acknowledge(User $user, string $notes = null): bool
    {
        if (!$this->canBeAcknowledged()) {
            return false;
        }

        $this->update([
            'status' => 'acknowledged',
            'acknowledged_by' => $user->id,
            'acknowledged_at' => now(),
            'acknowledgment_notes' => $notes,
        ]);

        return true;
    }

    /**
     * Resolve the alert.
     */
    public function resolve(User $user, string $notes = null): bool
    {
        if (!$this->canBeResolved()) {
            return false;
        }

        $this->update([
            'status' => 'resolved',
            'resolved_by' => $user->id,
            'resolved_at' => now(),
            'resolution_notes' => $notes,
        ]);

        return true;
    }

    /**
     * Check if the alert is still relevant.
     */
    public function isStillRelevant(): bool
    {
        $product = $this->product;
        
        if (!$product || !$product->track_inventory) {
            return false;
        }

        return match($this->type) {
            'low_stock' => $product->current_stock <= $product->min_stock_level,
            'out_of_stock' => $product->current_stock <= 0,
            'overstock' => $product->max_stock_level && $product->current_stock >= $product->max_stock_level,
            'expiry_warning' => $this->expiry_date && $this->expiry_date->isFuture(),
            default => true,
        };
    }

    /**
     * Auto-resolve alert if it's no longer relevant.
     */
    public function autoResolveIfNotRelevant(): bool
    {
        if ($this->status === 'resolved' || $this->isStillRelevant()) {
            return false;
        }

        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolution_notes' => 'تم الحل تلقائياً - لم يعد التنبيه ذا صلة',
        ]);

        return true;
    }
}
