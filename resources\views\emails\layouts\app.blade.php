<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $subject ?? __('app.company.name') }}</title>
    
    <style>
        /* Email-safe CSS */
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f8fafc;
            direction: rtl;
            text-align: right;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .email-header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            padding: 30px 20px;
            text-align: center;
        }
        
        .email-body {
            padding: 30px 20px;
            line-height: 1.6;
            color: #374151;
        }
        
        .email-footer {
            background-color: #f9fafb;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
            font-size: 12px;
            color: #6b7280;
        }
        
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3b82f6;
            color: #ffffff;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 16px 0;
        }
        
        .button:hover {
            background-color: #2563eb;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .mb-4 {
            margin-bottom: 16px;
        }
        
        .mt-4 {
            margin-top: 16px;
        }
        
        h1, h2, h3 {
            color: #1f2937;
            margin: 0 0 16px 0;
        }
        
        p {
            margin: 0 0 16px 0;
        }
        
        .logo-white {
            filter: brightness(0) invert(1);
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header with Logo -->
        <div class="email-header">
            <x-email-logo 
                size="default" 
                :showText="true" 
                class="logo-white" />
        </div>
        
        <!-- Email Content -->
        <div class="email-body">
            {{ $slot }}
        </div>
        
        <!-- Footer -->
        <div class="email-footer">
            <p style="margin: 0 0 8px 0;">
                <strong>{{ __('app.company.name') }}</strong>
            </p>
            <p style="margin: 0 0 8px 0;">
                {{ __('app.company.description') }}
            </p>
            <p style="margin: 0;">
                هذا البريد الإلكتروني تم إرساله تلقائياً، يرجى عدم الرد عليه.
            </p>
        </div>
    </div>
</body>
</html>
