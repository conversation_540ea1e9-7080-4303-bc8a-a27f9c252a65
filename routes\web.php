<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;

Route::view('/', 'welcome');

// Role-based dashboard routing
Route::get('dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Founder-only routes
Route::middleware(['auth', 'verified', 'role:founder'])->group(function () {
    Route::prefix('founder')->name('founder.')->group(function () {
        // System administration routes will be added here
    });
});

// Client Management Routes (Accessible by founders, admins, managers, employees)
Route::middleware(['auth', 'verified', 'role:founder,admin,manager,employee'])->group(function () {
    Route::prefix('clients')->name('clients.')->group(function () {
        Route::get('/', 'App\Http\Controllers\ClientController@index')->name('index');
        Route::get('/create', 'App\Http\Controllers\ClientController@create')->name('create');
        Route::post('/', 'App\Http\Controllers\ClientController@store')->name('store');
        Route::get('/{client}', 'App\Http\Controllers\ClientController@show')->name('show');
        Route::get('/{client}/edit', 'App\Http\Controllers\ClientController@edit')->name('edit');
        Route::put('/{client}', 'App\Http\Controllers\ClientController@update')->name('update');
        Route::delete('/{client}', 'App\Http\Controllers\ClientController@destroy')->name('destroy');

        // Client Contacts
        Route::prefix('{client}/contacts')->name('contacts.')->group(function () {
            Route::get('/', 'App\Http\Controllers\ClientContactController@index')->name('index');
            Route::post('/', 'App\Http\Controllers\ClientContactController@store')->name('store');
            Route::put('/{contact}', 'App\Http\Controllers\ClientContactController@update')->name('update');
            Route::delete('/{contact}', 'App\Http\Controllers\ClientContactController@destroy')->name('destroy');
        });

        // Client Documents
        Route::prefix('{client}/documents')->name('documents.')->group(function () {
            Route::get('/', 'App\Http\Controllers\ClientDocumentController@index')->name('index');
            Route::post('/', 'App\Http\Controllers\ClientDocumentController@store')->name('store');
            Route::get('/{document}/download', 'App\Http\Controllers\ClientDocumentController@download')->name('download');
            Route::delete('/{document}', 'App\Http\Controllers\ClientDocumentController@destroy')->name('destroy');
        });
    });
});

// Project Management Routes (Accessible by founders, admins, managers, employees)
Route::middleware(['auth', 'verified', 'role:founder,admin,manager,employee'])->group(function () {
    Route::prefix('projects')->name('projects.')->group(function () {
        Route::get('/', 'App\Http\Controllers\ProjectController@index')->name('index');
        Route::get('/create', 'App\Http\Controllers\ProjectController@create')->name('create');
        Route::post('/', 'App\Http\Controllers\ProjectController@store')->name('store');
        Route::get('/{project}', 'App\Http\Controllers\ProjectController@show')->name('show');
        Route::get('/{project}/edit', 'App\Http\Controllers\ProjectController@edit')->name('edit');
        Route::put('/{project}', 'App\Http\Controllers\ProjectController@update')->name('update');
        Route::delete('/{project}', 'App\Http\Controllers\ProjectController@destroy')->name('destroy');

        // Project Tasks
        Route::prefix('{project}/tasks')->name('tasks.')->group(function () {
            Route::get('/', 'App\Http\Controllers\ProjectTaskController@index')->name('index');
            Route::post('/', 'App\Http\Controllers\ProjectTaskController@store')->name('store');
            Route::put('/{task}', 'App\Http\Controllers\ProjectTaskController@update')->name('update');
            Route::delete('/{task}', 'App\Http\Controllers\ProjectTaskController@destroy')->name('destroy');
            Route::patch('/{task}/status', 'App\Http\Controllers\ProjectTaskController@updateStatus')->name('update-status');
        });

        // Project Files
        Route::prefix('{project}/files')->name('files.')->group(function () {
            Route::get('/', 'App\Http\Controllers\ProjectFileController@index')->name('index');
            Route::post('/', 'App\Http\Controllers\ProjectFileController@store')->name('store');
            Route::get('/{file}/download', 'App\Http\Controllers\ProjectFileController@download')->name('download');
            Route::delete('/{file}', 'App\Http\Controllers\ProjectFileController@destroy')->name('destroy');
        });
    });
});

// Financial Management Routes (Accessible by founders, admins, managers, employees)
Route::middleware(['auth', 'verified', 'role:founder,admin,manager,employee'])->group(function () {
    Route::prefix('invoices')->name('invoices.')->group(function () {
        Route::get('/', 'App\Http\Controllers\InvoiceController@index')->name('index');
        Route::get('/create', 'App\Http\Controllers\InvoiceController@create')->name('create');
        Route::post('/', 'App\Http\Controllers\InvoiceController@store')->name('store');
        Route::get('/{invoice}', 'App\Http\Controllers\InvoiceController@show')->name('show');
        Route::get('/{invoice}/edit', 'App\Http\Controllers\InvoiceController@edit')->name('edit');
        Route::put('/{invoice}', 'App\Http\Controllers\InvoiceController@update')->name('update');
        Route::delete('/{invoice}', 'App\Http\Controllers\InvoiceController@destroy')->name('destroy');
        Route::get('/{invoice}/pdf', 'App\Http\Controllers\InvoiceController@downloadPdf')->name('pdf');
        Route::post('/{invoice}/send', 'App\Http\Controllers\InvoiceController@send')->name('send');
        Route::patch('/{invoice}/status', 'App\Http\Controllers\InvoiceController@updateStatus')->name('update-status');

        // Invoice Items
        Route::prefix('{invoice}/items')->name('items.')->group(function () {
            Route::post('/', 'App\Http\Controllers\InvoiceItemController@store')->name('store');
            Route::put('/{item}', 'App\Http\Controllers\InvoiceItemController@update')->name('update');
            Route::delete('/{item}', 'App\Http\Controllers\InvoiceItemController@destroy')->name('destroy');
        });
    });

    Route::prefix('payments')->name('payments.')->group(function () {
        Route::get('/', 'App\Http\Controllers\PaymentController@index')->name('index');
        Route::get('/create', 'App\Http\Controllers\PaymentController@create')->name('create');
        Route::post('/', 'App\Http\Controllers\PaymentController@store')->name('store');
        Route::get('/{payment}', 'App\Http\Controllers\PaymentController@show')->name('show');
        Route::get('/{payment}/edit', 'App\Http\Controllers\PaymentController@edit')->name('edit');
        Route::put('/{payment}', 'App\Http\Controllers\PaymentController@update')->name('update');
        Route::delete('/{payment}', 'App\Http\Controllers\PaymentController@destroy')->name('destroy');
        Route::patch('/{payment}/status', 'App\Http\Controllers\PaymentController@updateStatus')->name('update-status');
    });
});

// Admin routes
Route::middleware(['auth', 'verified', 'role:founder,admin'])->group(function () {
    Route::prefix('admin')->name('admin.')->group(function () {
        // Administrative routes will be added here
    });
});

// Team Management routes
Route::middleware(['auth', 'verified', 'role:founder,admin,manager'])->group(function () {
    Route::prefix('team')->name('team.')->group(function () {
        Route::get('departments', App\Livewire\Team\DepartmentManagement::class)->name('departments');
        Route::get('members', App\Livewire\Team\TeamMemberManagement::class)->name('members');
        Route::get('attendance', App\Livewire\Team\AttendanceManagement::class)->name('attendance');
        Route::get('performance', App\Livewire\Team\PerformanceManagement::class)->name('performance');
    });
});

// Inventory Management routes
Route::middleware(['auth', 'verified', 'role:founder,admin,manager'])->group(function () {
    Route::prefix('inventory')->name('inventory.')->group(function () {
        Route::get('products', App\Livewire\Inventory\ProductManagement::class)->name('products');
        Route::get('categories', App\Livewire\Inventory\CategoryManagement::class)->name('categories');
        Route::get('movements', App\Livewire\Inventory\MovementManagement::class)->name('movements');
        Route::get('alerts', App\Livewire\Inventory\AlertManagement::class)->name('alerts');
    });
});

// Expense Management routes
Route::middleware(['auth', 'verified', 'role:founder,admin,manager,employee'])->group(function () {
    Route::prefix('expenses')->name('expenses.')->group(function () {
        Route::get('/', App\Livewire\Expenses\ExpenseManagement::class)->name('index');
        Route::get('categories', App\Livewire\Expenses\CategoryManagement::class)->name('categories');
        Route::get('budgets', App\Livewire\Expenses\BudgetManagement::class)->name('budgets');
        Route::get('reports', App\Livewire\Expenses\ExpenseReports::class)->name('reports');
    });
});

// Reports & Analytics routes
Route::middleware(['auth', 'verified', 'role:founder,admin,manager'])->group(function () {
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('dashboard', App\Livewire\Reports\ReportsDashboard::class)->name('dashboard');
        Route::get('financial', App\Livewire\Reports\FinancialReports::class)->name('financial');
        Route::get('inventory', App\Livewire\Reports\InventoryReports::class)->name('inventory');
        Route::get('team', App\Livewire\Reports\TeamReports::class)->name('team');
        Route::get('analytics', App\Livewire\Reports\Analytics::class)->name('analytics');
    });
});

// Manager routes
Route::middleware(['auth', 'verified', 'role:founder,admin,manager'])->group(function () {
    Route::prefix('manager')->name('manager.')->group(function () {
        // Management routes will be added here
    });
});

// Employee routes
Route::middleware(['auth', 'verified', 'role:founder,admin,manager,employee'])->group(function () {
    Route::prefix('employee')->name('employee.')->group(function () {
        // Employee routes will be added here
    });
});

// Client routes
Route::middleware(['auth', 'verified', 'role:client'])->group(function () {
    Route::prefix('client')->name('client.')->group(function () {
        // Client-specific routes will be added here
    });
});

Route::view('profile', 'profile')
    ->middleware(['auth'])
    ->name('profile');

// API routes for dashboard
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/api/dashboard/stats', [DashboardController::class, 'getStats'])->name('api.dashboard.stats');
    Route::get('/api/dashboard/activities', [DashboardController::class, 'getActivities'])->name('api.dashboard.activities');
    Route::get('/api/dashboard/charts', [DashboardController::class, 'getChartData'])->name('api.dashboard.charts');
});

require __DIR__.'/auth.php';
