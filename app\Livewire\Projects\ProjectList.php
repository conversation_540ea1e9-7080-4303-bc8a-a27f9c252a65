<?php

namespace App\Livewire\Projects;

use App\Models\Project;
use App\Models\Client;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Database\Eloquent\Builder;

class ProjectList extends Component
{
    use WithPagination;

    // Search and Filter Properties
    public $search = '';
    public $statusFilter = '';
    public $priorityFilter = '';
    public $clientFilter = '';
    public $projectManagerFilter = '';
    public $projectTypeFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 15;

    // UI State
    public $showFilters = false;
    public $selectedProjects = [];
    public $selectAll = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'priorityFilter' => ['except' => ''],
        'clientFilter' => ['except' => ''],
        'projectManagerFilter' => ['except' => ''],
        'projectTypeFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
        'page' => ['except' => 1],
    ];

    public function mount()
    {
        $this->authorize('viewAny', Project::class);
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingPriorityFilter()
    {
        $this->resetPage();
    }

    public function updatingClientFilter()
    {
        $this->resetPage();
    }

    public function updatingProjectManagerFilter()
    {
        $this->resetPage();
    }

    public function updatingProjectTypeFilter()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->statusFilter = '';
        $this->priorityFilter = '';
        $this->clientFilter = '';
        $this->projectManagerFilter = '';
        $this->projectTypeFilter = '';
        $this->resetPage();
    }

    public function toggleSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedProjects = $this->getProjectsQuery()->pluck('id')->toArray();
        } else {
            $this->selectedProjects = [];
        }
    }

    public function deleteSelected()
    {
        $this->authorize('delete', Project::class);
        
        $count = count($this->selectedProjects);
        Project::whereIn('id', $this->selectedProjects)->delete();
        
        $this->selectedProjects = [];
        $this->selectAll = false;
        
        session()->flash('success', "تم حذف {$count} مشروع بنجاح");
        $this->resetPage();
    }

    public function deleteProject($projectId)
    {
        $project = Project::findOrFail($projectId);
        $this->authorize('delete', $project);
        
        $projectName = $project->display_name;
        $project->delete();
        
        session()->flash('success', "تم حذف المشروع {$projectName} بنجاح");
        $this->resetPage();
    }

    public function updateProjectStatus($projectId, $status)
    {
        $project = Project::findOrFail($projectId);
        $this->authorize('updateStatus', $project);
        
        $project->update([
            'status' => $status,
            'updated_by' => auth()->id()
        ]);
        
        session()->flash('success', 'تم تحديث حالة المشروع بنجاح');
    }

    public function getProjectsQuery(): Builder
    {
        return Project::query()
            ->with(['client', 'projectManager', 'creator'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name_ar', 'like', '%' . $this->search . '%')
                      ->orWhere('name_en', 'like', '%' . $this->search . '%')
                      ->orWhere('project_code', 'like', '%' . $this->search . '%')
                      ->orWhereHas('client', function ($clientQuery) {
                          $clientQuery->where('name_ar', 'like', '%' . $this->search . '%')
                                     ->orWhere('company_name_ar', 'like', '%' . $this->search . '%');
                      });
                });
            })
            ->when($this->statusFilter, function ($query) {
                $query->where('status', $this->statusFilter);
            })
            ->when($this->priorityFilter, function ($query) {
                $query->where('priority', $this->priorityFilter);
            })
            ->when($this->clientFilter, function ($query) {
                $query->where('client_id', $this->clientFilter);
            })
            ->when($this->projectManagerFilter, function ($query) {
                $query->where('project_manager_id', $this->projectManagerFilter);
            })
            ->when($this->projectTypeFilter, function ($query) {
                $query->where('project_type', $this->projectTypeFilter);
            })
            ->orderBy($this->sortField, $this->sortDirection);
    }

    public function getProjectsProperty()
    {
        return $this->getProjectsQuery()->paginate($this->perPage);
    }

    public function getClientsProperty()
    {
        return Client::orderBy('name_ar')->get();
    }

    public function getProjectManagersProperty()
    {
        return User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['founder', 'admin', 'manager', 'employee']);
        })->orderBy('name')->get();
    }

    public function render()
    {
        return view('livewire.projects.project-list', [
            'projects' => $this->projects,
            'clients' => $this->clients,
            'projectManagers' => $this->projectManagers,
        ]);
    }
}
