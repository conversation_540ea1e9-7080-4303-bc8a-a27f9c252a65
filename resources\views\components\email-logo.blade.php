{{-- Email Logo Component --}}
@props([
    'size' => 'default', // small, default, large
    'showText' => true,
    'class' => ''
])

@php
    // Size configurations for email
    $sizeClasses = [
        'small' => 'height: 40px; width: auto;',
        'default' => 'height: 60px; width: auto;',
        'large' => 'height: 80px; width: auto;'
    ];
    
    $logoStyle = $sizeClasses[$size] ?? $sizeClasses['default'];
    
    // Text size configurations
    $textSizes = [
        'small' => ['title' => 'font-size: 16px;', 'subtitle' => 'font-size: 12px;'],
        'default' => ['title' => 'font-size: 20px;', 'subtitle' => 'font-size: 14px;'],
        'large' => ['title' => 'font-size: 24px;', 'subtitle' => 'font-size: 16px;']
    ];
    
    $textStyle = $textSizes[$size] ?? $textSizes['default'];
    
    // Check for logo file existence
    $hasLogo = file_exists(public_path('images/logo.png'));
    $logoUrl = $hasLogo ? asset('images/logo.png') : null;
@endphp

<table cellpadding="0" cellspacing="0" border="0" style="margin: 0; padding: 0; {{ $class }}">
    <tr>
        <td style="text-align: center; vertical-align: middle;">
            @if($hasLogo)
                <img src="{{ $logoUrl }}" 
                     alt="{{ __('app.company.name') }}" 
                     style="{{ $logoStyle }} object-fit: contain; display: block; margin: 0 auto;">
            @else
                {{-- Fallback for email - simple text logo --}}
                <div style="display: inline-block; text-align: center; font-family: Arial, sans-serif;">
                    <div style="{{ $textStyle['title'] }} font-weight: bold; color: #1e40af; margin: 0; padding: 0;">
                        {{ __('app.company.name_short', [], 'ar') ?? 'ليال' }}
                    </div>
                    @if($showText)
                        <div style="{{ $textStyle['subtitle'] }} color: #6b7280; margin: 0; padding: 0;">
                            {{ __('app.company.tagline_short', [], 'ar') ?? 'للتطوير البرمجي' }}
                        </div>
                    @endif
                </div>
            @endif
            
            @if($showText && $hasLogo)
                <div style="margin-top: 10px; text-align: center; font-family: Arial, sans-serif;">
                    <div style="{{ $textStyle['title'] }} font-weight: bold; color: #1e40af; margin: 0; padding: 0;">
                        {{ __('app.company.name_short', [], 'ar') ?? 'ليال' }}
                    </div>
                    <div style="{{ $textStyle['subtitle'] }} color: #6b7280; margin: 0; padding: 0;">
                        {{ __('app.company.tagline_short', [], 'ar') ?? 'للتطوير البرمجي' }}
                    </div>
                </div>
            @endif
        </td>
    </tr>
</table>
