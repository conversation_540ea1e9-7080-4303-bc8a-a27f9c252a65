<?php

namespace Database\Seeders;

use App\Models\Activity;
use App\Models\User;
use App\Models\Client;
use App\Models\Project;
use Illuminate\Database\Seeder;

class ActivitySeeder extends Seeder
{
    /**
     * Run the database seeders.
     */
    public function run(): void
    {
        // Get some users for activity logging
        $users = User::limit(5)->get();
        $clients = Client::limit(3)->get();
        $projects = Project::limit(3)->get();

        // Sample system activities
        $systemActivities = [
            [
                'type' => 'system',
                'action' => 'startup',
                'description_ar' => 'تم تشغيل النظام بنجاح',
                'description_en' => 'System started successfully',
            ],
            [
                'type' => 'system',
                'action' => 'maintenance',
                'description_ar' => 'تم تحديث قاعدة البيانات',
                'description_en' => 'Database updated',
            ],
            [
                'type' => 'system',
                'action' => 'backup',
                'description_ar' => 'تم إنشاء نسخة احتياطية',
                'description_en' => 'Backup created',
            ],
        ];

        // Sample user activities
        $userActivities = [
            [
                'type' => 'user',
                'action' => 'login',
                'description_ar' => 'تسجيل دخول المستخدم',
                'description_en' => 'User logged in',
            ],
            [
                'type' => 'user',
                'action' => 'profile_update',
                'description_ar' => 'تحديث الملف الشخصي',
                'description_en' => 'Profile updated',
            ],
            [
                'type' => 'user',
                'action' => 'password_change',
                'description_ar' => 'تغيير كلمة المرور',
                'description_en' => 'Password changed',
            ],
        ];

        // Sample client activities
        $clientActivities = [
            [
                'type' => 'client',
                'action' => 'created',
                'description_ar' => 'تم إضافة عميل جديد',
                'description_en' => 'New client added',
            ],
            [
                'type' => 'client',
                'action' => 'updated',
                'description_ar' => 'تم تحديث بيانات العميل',
                'description_en' => 'Client information updated',
            ],
            [
                'type' => 'client',
                'action' => 'contacted',
                'description_ar' => 'تم التواصل مع العميل',
                'description_en' => 'Client contacted',
            ],
        ];

        // Sample project activities
        $projectActivities = [
            [
                'type' => 'project',
                'action' => 'created',
                'description_ar' => 'تم إنشاء مشروع جديد',
                'description_en' => 'New project created',
            ],
            [
                'type' => 'project',
                'action' => 'updated',
                'description_ar' => 'تم تحديث المشروع',
                'description_en' => 'Project updated',
            ],
            [
                'type' => 'project',
                'action' => 'completed',
                'description_ar' => 'تم إكمال المشروع',
                'description_en' => 'Project completed',
            ],
        ];

        // Sample task activities
        $taskActivities = [
            [
                'type' => 'task',
                'action' => 'created',
                'description_ar' => 'تم إنشاء مهمة جديدة',
                'description_en' => 'New task created',
            ],
            [
                'type' => 'task',
                'action' => 'assigned',
                'description_ar' => 'تم تعيين المهمة',
                'description_en' => 'Task assigned',
            ],
            [
                'type' => 'task',
                'action' => 'completed',
                'description_ar' => 'تم إكمال المهمة',
                'description_en' => 'Task completed',
            ],
        ];

        // Sample invoice activities
        $invoiceActivities = [
            [
                'type' => 'invoice',
                'action' => 'created',
                'description_ar' => 'تم إنشاء فاتورة جديدة',
                'description_en' => 'New invoice created',
            ],
            [
                'type' => 'invoice',
                'action' => 'sent',
                'description_ar' => 'تم إرسال الفاتورة',
                'description_en' => 'Invoice sent',
            ],
            [
                'type' => 'invoice',
                'action' => 'paid',
                'description_ar' => 'تم دفع الفاتورة',
                'description_en' => 'Invoice paid',
            ],
        ];

        // Combine all activities
        $allActivities = array_merge(
            $systemActivities,
            $userActivities,
            $clientActivities,
            $projectActivities,
            $taskActivities,
            $invoiceActivities
        );

        // Create activities with random timestamps over the last 30 days
        foreach ($allActivities as $activityData) {
            $randomUser = $users->random();
            $randomClient = $clients->isNotEmpty() ? $clients->random() : null;
            $randomProject = $projects->isNotEmpty() ? $projects->random() : null;

            // Determine subject based on activity type
            $subject = null;
            if ($activityData['type'] === 'client' && $randomClient) {
                $subject = $randomClient;
            } elseif ($activityData['type'] === 'project' && $randomProject) {
                $subject = $randomProject;
            }

            Activity::create([
                'user_id' => $randomUser->id,
                'type' => $activityData['type'],
                'action' => $activityData['action'],
                'description_ar' => $activityData['description_ar'],
                'description_en' => $activityData['description_en'],
                'subject_type' => $subject ? get_class($subject) : null,
                'subject_id' => $subject?->id,
                'properties' => [
                    'sample_data' => true,
                    'seeded_at' => now()->toISOString(),
                ],
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Laravel Seeder',
                'created_at' => now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59)),
                'updated_at' => now(),
            ]);
        }

        // Create some recent activities for immediate testing
        for ($i = 0; $i < 5; $i++) {
            Activity::create([
                'user_id' => $users->random()->id,
                'type' => 'system',
                'action' => 'test',
                'description_ar' => "نشاط تجريبي رقم " . ($i + 1),
                'description_en' => "Test activity #" . ($i + 1),
                'properties' => [
                    'test_number' => $i + 1,
                    'created_for_testing' => true,
                ],
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Laravel Seeder',
                'created_at' => now()->subMinutes(rand(1, 60)),
                'updated_at' => now(),
            ]);
        }
    }
}
