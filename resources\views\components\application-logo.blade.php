{{-- Lial Company Logo Component --}}
@props([
    'size' => 'default', // small, default, large, xl
    'variant' => 'default', // default, compact, text-only, icon-only
    'clickable' => true,
    'showText' => true,
    'class' => ''
])

@php
    // Size configurations
    $sizeClasses = [
        'small' => 'h-8 w-auto',
        'default' => 'h-12 w-auto',
        'large' => 'h-16 w-auto',
        'xl' => 'h-20 w-auto'
    ];

    $logoClass = $sizeClasses[$size] ?? $sizeClasses['default'];

    // Text size configurations
    $textSizes = [
        'small' => ['title' => 'text-sm', 'subtitle' => 'text-xs'],
        'default' => ['title' => 'text-xl', 'subtitle' => 'text-xs'],
        'large' => ['title' => 'text-2xl', 'subtitle' => 'text-sm'],
        'xl' => ['title' => 'text-3xl', 'subtitle' => 'text-base']
    ];

    $textClass = $textSizes[$size] ?? $textSizes['default'];

    // Check for logo file existence
    $hasLogo = file_exists(public_path('images/logo.png')) || file_exists(public_path('logo/logo.png'));
    $logoPath = file_exists(public_path('images/logo.png')) ? 'images/logo.png' : 'logo/logo.png';
@endphp

<div class="logo-container flex items-center {{ $variant === 'compact' ? 'justify-start' : 'justify-center' }} {{ $class }}" {{ $attributes }}>
    @if($clickable)
        <a href="{{ auth()->check() ? route('dashboard') : '/' }}"
           class="flex items-center {{ $variant === 'compact' ? 'space-x-2 rtl:space-x-reverse' : ($showText ? 'space-x-3 rtl:space-x-reverse' : '') }} transition-opacity hover:opacity-80"
           wire:navigate>
    @else
        <div class="flex items-center {{ $variant === 'compact' ? 'space-x-2 rtl:space-x-reverse' : ($showText ? 'space-x-3 rtl:space-x-reverse' : '') }}">
    @endif

        {{-- Logo Image or Fallback --}}
        @if($variant !== 'text-only')
            @if($hasLogo)
                <img src="{{ asset($logoPath) }}"
                     alt="{{ __('app.company.name') }}"
                     class="{{ $logoClass }} object-contain"
                     loading="lazy">
            @else
                {{-- Fallback SVG Logo with Lial Branding --}}
                <svg class="{{ str_replace('w-auto', 'w-' . explode('-', $logoClass)[1], $logoClass) }} text-primary-600 flex-shrink-0"
                     viewBox="0 0 100 100"
                     xmlns="http://www.w3.org/2000/svg"
                     aria-label="{{ __('app.company.name') }}">
                    <defs>
                        <linearGradient id="lialGradient-{{ uniqid() }}" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    {{-- Modern geometric design representing technology and innovation --}}
                    <circle cx="50" cy="50" r="45" fill="url(#lialGradient-{{ uniqid() }})" opacity="0.1"/>
                    <path d="M25 25 L75 25 L75 35 L35 35 L35 75 L25 75 Z" fill="url(#lialGradient-{{ uniqid() }})"/>
                    <path d="M45 45 L75 45 L75 55 L55 55 L55 75 L45 75 Z" fill="url(#lialGradient-{{ uniqid() }})" opacity="0.8"/>
                    <circle cx="65" cy="65" r="8" fill="url(#lialGradient-{{ uniqid() }})"/>
                </svg>
            @endif
        @endif

        {{-- Company Text --}}
        @if($showText && $variant !== 'icon-only')
            <div class="text-right rtl:text-right {{ $variant === 'compact' ? 'hidden lg:block' : '' }}">
                <div class="{{ $textClass['title'] }} font-bold text-primary-600 font-arabic leading-tight">
                    {{ __('app.company.name_short', [], 'ar') ?? 'ليال' }}
                </div>
                @if($variant !== 'compact')
                    <div class="{{ $textClass['subtitle'] }} text-secondary-500 font-arabic leading-tight">
                        {{ __('app.company.tagline_short', [], 'ar') ?? 'للتطوير البرمجي' }}
                    </div>
                @endif
            </div>
        @endif

    @if($clickable)
        </a>
    @else
        </div>
    @endif
</div>
