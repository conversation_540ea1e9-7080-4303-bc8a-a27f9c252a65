<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="font-semibold text-xl text-secondary-800 leading-tight">
                {{ __('إدارة المشاريع') }}
            </h2>
            <div class="flex items-center space-x-2 space-x-reverse">
                @can('create', App\Models\Project::class)
                    <a href="{{ route('projects.create') }}" 
                       class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                        <x-icon name="plus" class="w-4 h-4 ml-2" />
                        إضافة مشروع جديد
                    </a>
                @endcan
                
                @can('export', App\Models\Project::class)
                    <button type="button" 
                            class="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                        <x-icon name="arrow-down-tray" class="w-4 h-4 ml-2" />
                        تصدير
                    </button>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="clipboard-document-list" class="w-5 h-5 text-blue-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">إجمالي المشاريع</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\Project::count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="play" class="w-5 h-5 text-green-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">المشاريع النشطة</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\Project::where('status', 'active')->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="clock" class="w-5 h-5 text-yellow-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">قيد التخطيط</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\Project::where('status', 'planning')->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="check-circle" class="w-5 h-5 text-purple-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">مكتملة</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\Project::where('status', 'completed')->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project List Component -->
            <div class="bg-white shadow-sm rounded-lg border border-secondary-200">
                <livewire:projects.project-list />
            </div>
        </div>
    </div>
</x-app-layout>
