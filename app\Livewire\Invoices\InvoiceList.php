<?php

namespace App\Livewire\Invoices;

use App\Models\Invoice;
use App\Models\Client;
use App\Models\Project;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Database\Eloquent\Builder;

class InvoiceList extends Component
{
    use WithPagination;

    // Search and Filter Properties
    public $search = '';
    public $statusFilter = '';
    public $paymentStatusFilter = '';
    public $typeFilter = '';
    public $clientFilter = '';
    public $projectFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 15;

    // UI State
    public $showFilters = false;
    public $selectedInvoices = [];
    public $selectAll = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'paymentStatusFilter' => ['except' => ''],
        'typeFilter' => ['except' => ''],
        'clientFilter' => ['except' => ''],
        'projectFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
        'page' => ['except' => 1],
    ];

    public function mount()
    {
        $this->authorize('viewAny', Invoice::class);
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingPaymentStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingTypeFilter()
    {
        $this->resetPage();
    }

    public function updatingClientFilter()
    {
        $this->resetPage();
    }

    public function updatingProjectFilter()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->statusFilter = '';
        $this->paymentStatusFilter = '';
        $this->typeFilter = '';
        $this->clientFilter = '';
        $this->projectFilter = '';
        $this->resetPage();
    }

    public function toggleSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedInvoices = $this->getInvoicesQuery()->pluck('id')->toArray();
        } else {
            $this->selectedInvoices = [];
        }
    }

    public function deleteSelected()
    {
        $this->authorize('delete', Invoice::class);
        
        $count = count($this->selectedInvoices);
        Invoice::whereIn('id', $this->selectedInvoices)->delete();
        
        $this->selectedInvoices = [];
        $this->selectAll = false;
        
        session()->flash('success', "تم حذف {$count} فاتورة بنجاح");
        $this->resetPage();
    }

    public function deleteInvoice($invoiceId)
    {
        $invoice = Invoice::findOrFail($invoiceId);
        $this->authorize('delete', $invoice);
        
        $invoiceNumber = $invoice->invoice_number;
        $invoice->delete();
        
        session()->flash('success', "تم حذف الفاتورة {$invoiceNumber} بنجاح");
        $this->resetPage();
    }

    public function updateInvoiceStatus($invoiceId, $status)
    {
        $invoice = Invoice::findOrFail($invoiceId);
        $this->authorize('updateStatus', $invoice);
        
        $invoice->update([
            'status' => $status,
            'updated_by' => auth()->id()
        ]);
        
        session()->flash('success', 'تم تحديث حالة الفاتورة بنجاح');
    }

    public function sendInvoice($invoiceId)
    {
        $invoice = Invoice::findOrFail($invoiceId);
        $this->authorize('send', $invoice);
        
        $invoice->update([
            'status' => 'sent',
            'sent_at' => now(),
            'updated_by' => auth()->id()
        ]);
        
        session()->flash('success', 'تم إرسال الفاتورة بنجاح');
    }

    public function getInvoicesQuery(): Builder
    {
        return Invoice::query()
            ->with(['client', 'project', 'creator'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('invoice_number', 'like', '%' . $this->search . '%')
                      ->orWhere('subject_ar', 'like', '%' . $this->search . '%')
                      ->orWhere('subject_en', 'like', '%' . $this->search . '%')
                      ->orWhereHas('client', function ($clientQuery) {
                          $clientQuery->where('name_ar', 'like', '%' . $this->search . '%')
                                     ->orWhere('company_name_ar', 'like', '%' . $this->search . '%');
                      });
                });
            })
            ->when($this->statusFilter, function ($query) {
                $query->where('status', $this->statusFilter);
            })
            ->when($this->paymentStatusFilter, function ($query) {
                $query->where('payment_status', $this->paymentStatusFilter);
            })
            ->when($this->typeFilter, function ($query) {
                $query->where('type', $this->typeFilter);
            })
            ->when($this->clientFilter, function ($query) {
                $query->where('client_id', $this->clientFilter);
            })
            ->when($this->projectFilter, function ($query) {
                $query->where('project_id', $this->projectFilter);
            })
            ->orderBy($this->sortField, $this->sortDirection);
    }

    public function getInvoicesProperty()
    {
        return $this->getInvoicesQuery()->paginate($this->perPage);
    }

    public function getClientsProperty()
    {
        return Client::orderBy('name_ar')->get();
    }

    public function getProjectsProperty()
    {
        return Project::orderBy('name_ar')->get();
    }

    public function render()
    {
        return view('livewire.invoices.invoice-list', [
            'invoices' => $this->invoices,
            'clients' => $this->clients,
            'projects' => $this->projects,
        ]);
    }
}
