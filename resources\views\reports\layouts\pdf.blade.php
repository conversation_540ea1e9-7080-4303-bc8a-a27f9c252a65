<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $title ?? 'تقرير' }} - {{ __('app.company.name') }}</title>
    
    <style>
        @page {
            margin: 2cm 1.5cm;
            size: A4;
        }
        
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.4;
            color: #333;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 0;
        }
        
        .header {
            border-bottom: 2px solid #1e40af;
            padding-bottom: 20px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-logo {
            flex: 0 0 auto;
        }
        
        .header-info {
            flex: 1;
            text-align: right;
            margin-right: 20px;
        }
        
        .company-name {
            font-size: 18pt;
            font-weight: bold;
            color: #1e40af;
            margin: 0 0 5px 0;
        }
        
        .company-tagline {
            font-size: 10pt;
            color: #666;
            margin: 0;
        }
        
        .report-title {
            font-size: 16pt;
            font-weight: bold;
            color: #1f2937;
            margin: 20px 0 10px 0;
            text-align: center;
        }
        
        .report-meta {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #e2e8f0;
        }
        
        .report-meta table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .report-meta td {
            padding: 5px 10px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .report-meta td:first-child {
            font-weight: bold;
            width: 30%;
            color: #4a5568;
        }
        
        .content {
            margin: 20px 0;
        }
        
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            border-top: 1px solid #e2e8f0;
            padding: 10px 0;
            text-align: center;
            font-size: 9pt;
            color: #666;
            background-color: white;
        }
        
        .page-number:before {
            content: "صفحة " counter(page) " من " counter(pages);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        th, td {
            border: 1px solid #d1d5db;
            padding: 8px 12px;
            text-align: right;
        }
        
        th {
            background-color: #f3f4f6;
            font-weight: bold;
            color: #374151;
        }
        
        tr:nth-child(even) {
            background-color: #f9fafb;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-left {
            text-align: left;
        }
        
        .font-bold {
            font-weight: bold;
        }
        
        .text-primary {
            color: #1e40af;
        }
        
        .text-secondary {
            color: #6b7280;
        }
        
        .mb-4 {
            margin-bottom: 16px;
        }
        
        .mt-4 {
            margin-top: 16px;
        }
        
        .break-page {
            page-break-before: always;
        }
        
        .no-break {
            page-break-inside: avoid;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-logo">
            <x-print-logo 
                size="default" 
                :showText="false" />
        </div>
        <div class="header-info">
            <div class="company-name">{{ __('app.company.name') }}</div>
            <div class="company-tagline">{{ __('app.company.tagline') }}</div>
        </div>
    </div>
    
    <!-- Report Title -->
    <h1 class="report-title">{{ $title ?? 'تقرير' }}</h1>
    
    <!-- Report Metadata -->
    <div class="report-meta">
        <table>
            <tr>
                <td>تاريخ التقرير:</td>
                <td>{{ $reportDate ?? now()->format('Y-m-d H:i') }}</td>
            </tr>
            @if(isset($reportPeriod))
            <tr>
                <td>فترة التقرير:</td>
                <td>{{ $reportPeriod }}</td>
            </tr>
            @endif
            @if(isset($generatedBy))
            <tr>
                <td>تم إنشاؤه بواسطة:</td>
                <td>{{ $generatedBy }}</td>
            </tr>
            @endif
            @if(isset($reportType))
            <tr>
                <td>نوع التقرير:</td>
                <td>{{ $reportType }}</td>
            </tr>
            @endif
        </table>
    </div>
    
    <!-- Report Content -->
    <div class="content">
        {{ $slot }}
    </div>
    
    <!-- Footer -->
    <div class="footer">
        <div class="page-number"></div>
        <div style="margin-top: 5px; font-size: 8pt;">
            تم إنشاء هذا التقرير بواسطة نظام {{ __('app.company.name') }} - {{ now()->format('Y-m-d H:i:s') }}
        </div>
    </div>
</body>
</html>
