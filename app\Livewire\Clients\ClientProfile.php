<?php

namespace App\Livewire\Clients;

use App\Models\Client;
use Livewire\Component;

class ClientProfile extends Component
{
    public Client $client;
    public $activeTab = 'overview';

    protected $queryString = [
        'activeTab' => ['except' => 'overview'],
    ];

    public function mount(Client $client)
    {
        $this->authorize('view', $client);
        $this->client = $client;
        
        // Load relationships
        $this->client->load([
            'contacts',
            'documents',
            'communications' => function ($query) {
                $query->latest()->limit(10);
            },
            'projects' => function ($query) {
                $query->latest()->limit(5);
            },
            'invoices' => function ($query) {
                $query->latest()->limit(5);
            },
            'assignedUser',
            'creator'
        ]);
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function render()
    {
        return view('livewire.clients.client-profile');
    }
}
