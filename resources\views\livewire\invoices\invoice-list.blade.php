<div>
    <!-- Search and Filters Header -->
    <div class="p-6 border-b border-secondary-200">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- Search -->
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <x-icon name="magnifying-glass" class="h-5 w-5 text-secondary-400" />
                    </div>
                    <input wire:model.live.debounce.300ms="search" 
                           type="text" 
                           class="block w-full pr-10 pl-3 py-2 border border-secondary-300 rounded-lg leading-5 bg-white placeholder-secondary-500 focus:outline-none focus:placeholder-secondary-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 text-sm"
                           placeholder="البحث في الفواتير...">
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center space-x-3 space-x-reverse">
                <button wire:click="$toggle('showFilters')" 
                        class="inline-flex items-center px-3 py-2 border border-secondary-300 rounded-lg text-sm font-medium text-secondary-700 bg-white hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <x-icon name="funnel" class="w-4 h-4 ml-2" />
                    فلترة
                    @if($showFilters)
                        <x-icon name="chevron-up" class="w-4 h-4 mr-2" />
                    @else
                        <x-icon name="chevron-down" class="w-4 h-4 mr-2" />
                    @endif
                </button>

                @if(count($selectedInvoices) > 0)
                    <button wire:click="deleteSelected" 
                            wire:confirm="هل أنت متأكد من حذف الفواتير المحددة؟"
                            class="inline-flex items-center px-3 py-2 border border-red-300 rounded-lg text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <x-icon name="trash" class="w-4 h-4 ml-2" />
                        حذف المحدد ({{ count($selectedInvoices) }})
                    </button>
                @endif

                <button wire:click="clearFilters" 
                        class="inline-flex items-center px-3 py-2 border border-secondary-300 rounded-lg text-sm font-medium text-secondary-700 bg-white hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <x-icon name="x-mark" class="w-4 h-4 ml-2" />
                    مسح الفلاتر
                </button>
            </div>
        </div>

        <!-- Filters Panel -->
        @if($showFilters)
            <div class="mt-4 p-4 bg-secondary-50 rounded-lg border border-secondary-200">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">الحالة</label>
                        <select wire:model.live="statusFilter" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">جميع الحالات</option>
                            <option value="draft">مسودة</option>
                            <option value="sent">مرسلة</option>
                            <option value="viewed">مشاهدة</option>
                            <option value="approved">موافق عليها</option>
                            <option value="paid">مدفوعة</option>
                            <option value="partially_paid">مدفوعة جزئياً</option>
                            <option value="overdue">متأخرة</option>
                            <option value="cancelled">ملغية</option>
                            <option value="refunded">مستردة</option>
                        </select>
                    </div>

                    <!-- Payment Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">حالة الدفع</label>
                        <select wire:model.live="paymentStatusFilter" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">جميع حالات الدفع</option>
                            <option value="unpaid">غير مدفوعة</option>
                            <option value="partially_paid">مدفوعة جزئياً</option>
                            <option value="paid">مدفوعة</option>
                            <option value="refunded">مستردة</option>
                        </select>
                    </div>

                    <!-- Type Filter -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">النوع</label>
                        <select wire:model.live="typeFilter" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">جميع الأنواع</option>
                            <option value="invoice">فاتورة</option>
                            <option value="quote">عرض سعر</option>
                            <option value="proforma">فاتورة أولية</option>
                            <option value="credit_note">إشعار دائن</option>
                            <option value="debit_note">إشعار مدين</option>
                        </select>
                    </div>

                    <!-- Client Filter -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">العميل</label>
                        <select wire:model.live="clientFilter" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">جميع العملاء</option>
                            @foreach($clients as $client)
                                <option value="{{ $client->id }}">{{ $client->display_name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Project Filter -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-1">المشروع</label>
                        <select wire:model.live="projectFilter" class="block w-full px-3 py-2 border border-secondary-300 rounded-lg text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">جميع المشاريع</option>
                            @foreach($projects as $project)
                                <option value="{{ $project->id }}">{{ $project->display_name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-secondary-200">
            <thead class="bg-secondary-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                        <input type="checkbox" 
                               wire:model.live="selectAll" 
                               wire:click="toggleSelectAll"
                               class="rounded border-secondary-300 text-primary-600 focus:ring-primary-500">
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider cursor-pointer" wire:click="sortBy('invoice_number')">
                        <div class="flex items-center">
                            رقم الفاتورة
                            @if($sortField === 'invoice_number')
                                @if($sortDirection === 'asc')
                                    <x-icon name="chevron-up" class="w-4 h-4 mr-1" />
                                @else
                                    <x-icon name="chevron-down" class="w-4 h-4 mr-1" />
                                @endif
                            @endif
                        </div>
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">العميل</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">النوع</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider cursor-pointer" wire:click="sortBy('total_amount')">
                        <div class="flex items-center">
                            المبلغ
                            @if($sortField === 'total_amount')
                                @if($sortDirection === 'asc')
                                    <x-icon name="chevron-up" class="w-4 h-4 mr-1" />
                                @else
                                    <x-icon name="chevron-down" class="w-4 h-4 mr-1" />
                                @endif
                            @endif
                        </div>
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider cursor-pointer" wire:click="sortBy('status')">
                        <div class="flex items-center">
                            الحالة
                            @if($sortField === 'status')
                                @if($sortDirection === 'asc')
                                    <x-icon name="chevron-up" class="w-4 h-4 mr-1" />
                                @else
                                    <x-icon name="chevron-down" class="w-4 h-4 mr-1" />
                                @endif
                            @endif
                        </div>
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">حالة الدفع</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider cursor-pointer" wire:click="sortBy('due_date')">
                        <div class="flex items-center">
                            تاريخ الاستحقاق
                            @if($sortField === 'due_date')
                                @if($sortDirection === 'asc')
                                    <x-icon name="chevron-up" class="w-4 h-4 mr-1" />
                                @else
                                    <x-icon name="chevron-down" class="w-4 h-4 mr-1" />
                                @endif
                            @endif
                        </div>
                    </th>
                    <th scope="col" class="relative px-6 py-3">
                        <span class="sr-only">الإجراءات</span>
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-secondary-200">
                @forelse($invoices as $invoice)
                    <tr class="hover:bg-secondary-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" 
                                   wire:model.live="selectedInvoices" 
                                   value="{{ $invoice->id }}"
                                   class="rounded border-secondary-300 text-primary-600 focus:ring-primary-500">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-lg bg-primary-100 flex items-center justify-center">
                                        <x-icon name="document-text" class="w-5 h-5 text-primary-600" />
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-secondary-900">
                                        <a href="{{ route('invoices.show', $invoice) }}" class="hover:text-primary-600">
                                            {{ $invoice->invoice_number }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-secondary-500">{{ $invoice->subject_ar }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-secondary-900">{{ $invoice->client->display_name }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @php
                                $typeLabels = [
                                    'invoice' => 'فاتورة',
                                    'quote' => 'عرض سعر',
                                    'proforma' => 'فاتورة أولية',
                                    'credit_note' => 'إشعار دائن',
                                    'debit_note' => 'إشعار مدين',
                                ];
                            @endphp
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ $typeLabels[$invoice->type] ?? $invoice->type }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-secondary-900">
                                {{ number_format($invoice->total_amount, 2) }} {{ $invoice->currency }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @php
                                $statusColors = [
                                    'draft' => 'bg-gray-100 text-gray-800',
                                    'sent' => 'bg-blue-100 text-blue-800',
                                    'viewed' => 'bg-indigo-100 text-indigo-800',
                                    'approved' => 'bg-green-100 text-green-800',
                                    'paid' => 'bg-green-100 text-green-800',
                                    'partially_paid' => 'bg-yellow-100 text-yellow-800',
                                    'overdue' => 'bg-red-100 text-red-800',
                                    'cancelled' => 'bg-gray-100 text-gray-800',
                                    'refunded' => 'bg-purple-100 text-purple-800',
                                ];
                                $statusLabels = [
                                    'draft' => 'مسودة',
                                    'sent' => 'مرسلة',
                                    'viewed' => 'مشاهدة',
                                    'approved' => 'موافق عليها',
                                    'paid' => 'مدفوعة',
                                    'partially_paid' => 'مدفوعة جزئياً',
                                    'overdue' => 'متأخرة',
                                    'cancelled' => 'ملغية',
                                    'refunded' => 'مستردة',
                                ];
                            @endphp
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusColors[$invoice->status] ?? 'bg-gray-100 text-gray-800' }}">
                                {{ $statusLabels[$invoice->status] ?? $invoice->status }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @php
                                $paymentStatusColors = [
                                    'unpaid' => 'bg-red-100 text-red-800',
                                    'partially_paid' => 'bg-yellow-100 text-yellow-800',
                                    'paid' => 'bg-green-100 text-green-800',
                                    'refunded' => 'bg-purple-100 text-purple-800',
                                ];
                                $paymentStatusLabels = [
                                    'unpaid' => 'غير مدفوعة',
                                    'partially_paid' => 'مدفوعة جزئياً',
                                    'paid' => 'مدفوعة',
                                    'refunded' => 'مستردة',
                                ];
                            @endphp
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $paymentStatusColors[$invoice->payment_status] ?? 'bg-gray-100 text-gray-800' }}">
                                {{ $paymentStatusLabels[$invoice->payment_status] ?? $invoice->payment_status }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                            {{ $invoice->due_date?->format('Y/m/d') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-left text-sm font-medium">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                @can('view', $invoice)
                                    <a href="{{ route('invoices.show', $invoice) }}" 
                                       class="text-primary-600 hover:text-primary-900">
                                        <x-icon name="eye" class="w-4 h-4" />
                                    </a>
                                @endcan
                                
                                @can('downloadPdf', $invoice)
                                    <a href="{{ route('invoices.pdf', $invoice) }}" 
                                       class="text-blue-600 hover:text-blue-900">
                                        <x-icon name="arrow-down-tray" class="w-4 h-4" />
                                    </a>
                                @endcan
                                
                                @can('send', $invoice)
                                    @if($invoice->status === 'draft')
                                        <button wire:click="sendInvoice({{ $invoice->id }})" 
                                                class="text-green-600 hover:text-green-900">
                                            <x-icon name="paper-airplane" class="w-4 h-4" />
                                        </button>
                                    @endif
                                @endcan
                                
                                @can('update', $invoice)
                                    <a href="{{ route('invoices.edit', $invoice) }}" 
                                       class="text-secondary-600 hover:text-secondary-900">
                                        <x-icon name="pencil" class="w-4 h-4" />
                                    </a>
                                @endcan
                                
                                @can('delete', $invoice)
                                    <button wire:click="deleteInvoice({{ $invoice->id }})" 
                                            wire:confirm="هل أنت متأكد من حذف هذه الفاتورة؟"
                                            class="text-red-600 hover:text-red-900">
                                        <x-icon name="trash" class="w-4 h-4" />
                                    </button>
                                @endcan
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="9" class="px-6 py-12 text-center">
                            <div class="flex flex-col items-center">
                                <x-icon name="document-text" class="w-12 h-12 text-secondary-400 mb-4" />
                                <h3 class="text-lg font-medium text-secondary-900 mb-2">لا توجد فواتير</h3>
                                <p class="text-secondary-500 mb-4">لم يتم العثور على أي فواتير مطابقة للبحث أو الفلاتر المحددة.</p>
                                @can('create', App\Models\Invoice::class)
                                    <a href="{{ route('invoices.create') }}" 
                                       class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                                        إنشاء فاتورة جديدة
                                    </a>
                                @endcan
                            </div>
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    @if($invoices->hasPages())
        <div class="px-6 py-4 border-t border-secondary-200">
            {{ $invoices->links() }}
        </div>
    @endif
</div>
