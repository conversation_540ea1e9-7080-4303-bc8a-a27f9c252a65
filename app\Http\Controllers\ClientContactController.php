<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\ClientContact;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ClientContactController extends Controller
{
    /**
     * Display a listing of client contacts.
     */
    public function index(Client $client): JsonResponse
    {
        $this->authorize('manageContacts', $client);

        $contacts = $client->contacts()->orderBy('is_primary', 'desc')->get();

        return response()->json($contacts);
    }

    /**
     * Store a newly created contact in storage.
     */
    public function store(Request $request, Client $client): JsonResponse
    {
        $this->authorize('manageContacts', $client);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'is_primary' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        // If this is set as primary, unset other primary contacts
        if ($validated['is_primary'] ?? false) {
            $client->contacts()->update(['is_primary' => false]);
        }

        $contact = $client->contacts()->create($validated);

        return response()->json([
            'success' => true,
            'message' => 'تم إضافة جهة الاتصال بنجاح',
            'contact' => $contact
        ]);
    }

    /**
     * Update the specified contact in storage.
     */
    public function update(Request $request, Client $client, ClientContact $contact): JsonResponse
    {
        $this->authorize('manageContacts', $client);

        // Ensure the contact belongs to this client
        if ($contact->client_id !== $client->id) {
            abort(404);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'is_primary' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        // If this is set as primary, unset other primary contacts
        if ($validated['is_primary'] ?? false) {
            $client->contacts()->where('id', '!=', $contact->id)->update(['is_primary' => false]);
        }

        $contact->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث جهة الاتصال بنجاح',
            'contact' => $contact
        ]);
    }

    /**
     * Remove the specified contact from storage.
     */
    public function destroy(Client $client, ClientContact $contact): JsonResponse
    {
        $this->authorize('manageContacts', $client);

        // Ensure the contact belongs to this client
        if ($contact->client_id !== $client->id) {
            abort(404);
        }

        $contactName = $contact->name;
        $contact->delete();

        return response()->json([
            'success' => true,
            'message' => "تم حذف جهة الاتصال {$contactName} بنجاح"
        ]);
    }
}
