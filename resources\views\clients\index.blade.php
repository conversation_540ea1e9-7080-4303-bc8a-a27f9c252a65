<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="font-semibold text-xl text-secondary-800 leading-tight">
                {{ __('إدارة العملاء') }}
            </h2>
            <div class="flex items-center space-x-2 space-x-reverse">
                @can('create', App\Models\Client::class)
                    <a href="{{ route('clients.create') }}" 
                       class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                        <x-icon name="plus" class="w-4 h-4 ml-2" />
                        إضافة عميل جديد
                    </a>
                @endcan
                
                @can('export', App\Models\Client::class)
                    <button type="button" 
                            class="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                        <x-icon name="arrow-down-tray" class="w-4 h-4 ml-2" />
                        تصدير
                    </button>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="users" class="w-5 h-5 text-blue-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">إجمالي العملاء</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\Client::count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="check-circle" class="w-5 h-5 text-green-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">العملاء النشطون</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\Client::where('status', 'active')->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="clock" class="w-5 h-5 text-yellow-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">عملاء محتملون</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\Client::where('status', 'prospect')->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="exclamation-triangle" class="w-5 h-5 text-red-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">يحتاج متابعة</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\Client::whereNotNull('next_follow_up_at')->where('next_follow_up_at', '<=', now())->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client List Component -->
            <div class="bg-white shadow-sm rounded-lg border border-secondary-200">
                <livewire:clients.client-list />
            </div>
        </div>
    </div>
</x-app-layout>
