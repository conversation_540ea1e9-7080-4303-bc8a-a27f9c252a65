<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" class="rtl">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Lial ERP') }} - {{ __('app.auth.login_title') }}</title>
        <meta name="description" content="{{ __('app.company.description') }}">

        <!-- Favicon -->
        <link rel="icon" type="image/png" href="{{ asset('images/logo.png') }}">
        <link rel="apple-touch-icon" href="{{ asset('images/logo.png') }}">

        <!-- Arabic Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-arabic text-secondary-800 antialiased bg-gradient-to-br from-primary-50 via-white to-secondary-50">
        <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0">
            {{-- Header with Logo and Company Info --}}
            <div class="text-center mb-8">
                <x-application-logo
                    size="xl"
                    variant="default"
                    :clickable="true"
                    :showText="true"
                    class="mb-4" />
                <p class="text-secondary-600 text-sm mt-2">{{ __('app.company.tagline') }}</p>
            </div>

            {{-- Authentication Card --}}
            <div class="w-full sm:max-w-md">
                <div class="bg-white shadow-xl rounded-2xl overflow-hidden border border-secondary-100">
                    {{-- Card Header --}}
                    <div class="bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-4">
                        <h2 class="text-white text-lg font-semibold text-center">
                            {{ $title ?? __('app.auth.login_title') }}
                        </h2>
                    </div>

                    {{-- Card Body --}}
                    <div class="px-6 py-8">
                        {{ $slot }}
                    </div>
                </div>

                {{-- Footer --}}
                <div class="text-center mt-6 text-sm text-secondary-500">
                    <p>&copy; {{ date('Y') }} {{ __('app.company.name') }}. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </body>
</html>
