<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateClientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('update', $this->route('client'));
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $client = $this->route('client');
        
        return [
            // Basic Information
            'type' => ['required', 'in:individual,company'],
            'name_ar' => ['required_if:type,individual', 'string', 'max:255'],
            'name_en' => ['nullable', 'string', 'max:255'],
            'email' => ['required', 'email', Rule::unique('clients')->ignore($client->id)],
            'phone' => ['nullable', 'string', 'max:20'],
            'mobile' => ['nullable', 'string', 'max:20'],
            'whatsapp' => ['nullable', 'string', 'max:20'],
            
            // Individual Information
            'birth_date' => ['nullable', 'date', 'before:today'],
            'gender' => ['nullable', 'in:male,female'],
            'nationality' => ['nullable', 'string', 'max:100'],
            'id_number' => ['nullable', 'string', 'max:20', Rule::unique('clients')->ignore($client->id)],
            
            // Company Information
            'company_name_ar' => ['required_if:type,company', 'string', 'max:255'],
            'company_name_en' => ['nullable', 'string', 'max:255'],
            'commercial_register' => ['nullable', 'string', 'max:50', Rule::unique('clients')->ignore($client->id)],
            'tax_number' => ['nullable', 'string', 'max:50', Rule::unique('clients')->ignore($client->id)],
            'industry' => ['nullable', 'string', 'max:100'],
            'company_size' => ['nullable', 'integer', 'min:1'],
            'website' => ['nullable', 'url', 'max:255'],
            
            // Address Information
            'country' => ['nullable', 'string', 'max:100'],
            'city' => ['nullable', 'string', 'max:100'],
            'district' => ['nullable', 'string', 'max:100'],
            'address' => ['nullable', 'string', 'max:500'],
            'postal_code' => ['nullable', 'string', 'max:20'],
            'latitude' => ['nullable', 'numeric', 'between:-90,90'],
            'longitude' => ['nullable', 'numeric', 'between:-180,180'],
            
            // Business Information
            'status' => ['required', 'in:active,inactive,prospect,suspended'],
            'priority' => ['required', 'in:low,medium,high,critical'],
            'source' => ['nullable', 'string', 'max:100'],
            'referral_source' => ['nullable', 'string', 'max:255'],
            'credit_limit' => ['nullable', 'numeric', 'min:0'],
            'payment_terms' => ['nullable', 'integer', 'min:0', 'max:365'],
            'notes' => ['nullable', 'string', 'max:1000'],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['string', 'max:50'],
            'custom_fields' => ['nullable', 'array'],
            'assigned_to' => ['nullable', 'exists:users,id'],
            'next_follow_up_at' => ['nullable', 'date', 'after:now'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'type.required' => 'نوع العميل مطلوب',
            'type.in' => 'نوع العميل يجب أن يكون فرد أو شركة',
            'name_ar.required_if' => 'اسم العميل مطلوب للأفراد',
            'name_ar.max' => 'اسم العميل لا يجب أن يتجاوز 255 حرف',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.unique' => 'البريد الإلكتروني مستخدم من قبل',
            'phone.max' => 'رقم الهاتف لا يجب أن يتجاوز 20 رقم',
            'mobile.max' => 'رقم الجوال لا يجب أن يتجاوز 20 رقم',
            'birth_date.date' => 'تاريخ الميلاد غير صحيح',
            'birth_date.before' => 'تاريخ الميلاد يجب أن يكون في الماضي',
            'gender.in' => 'الجنس يجب أن يكون ذكر أو أنثى',
            'id_number.unique' => 'رقم الهوية مستخدم من قبل',
            'company_name_ar.required_if' => 'اسم الشركة مطلوب للشركات',
            'company_name_ar.max' => 'اسم الشركة لا يجب أن يتجاوز 255 حرف',
            'commercial_register.unique' => 'رقم السجل التجاري مستخدم من قبل',
            'tax_number.unique' => 'الرقم الضريبي مستخدم من قبل',
            'company_size.integer' => 'حجم الشركة يجب أن يكون رقم صحيح',
            'company_size.min' => 'حجم الشركة يجب أن يكون أكبر من صفر',
            'website.url' => 'رابط الموقع غير صحيح',
            'latitude.numeric' => 'خط العرض يجب أن يكون رقم',
            'latitude.between' => 'خط العرض يجب أن يكون بين -90 و 90',
            'longitude.numeric' => 'خط الطول يجب أن يكون رقم',
            'longitude.between' => 'خط الطول يجب أن يكون بين -180 و 180',
            'status.required' => 'حالة العميل مطلوبة',
            'status.in' => 'حالة العميل غير صحيحة',
            'priority.required' => 'أولوية العميل مطلوبة',
            'priority.in' => 'أولوية العميل غير صحيحة',
            'credit_limit.numeric' => 'حد الائتمان يجب أن يكون رقم',
            'credit_limit.min' => 'حد الائتمان لا يمكن أن يكون سالب',
            'payment_terms.integer' => 'شروط الدفع يجب أن تكون رقم صحيح',
            'payment_terms.min' => 'شروط الدفع لا يمكن أن تكون سالبة',
            'payment_terms.max' => 'شروط الدفع لا يمكن أن تتجاوز 365 يوم',
            'notes.max' => 'الملاحظات لا يجب أن تتجاوز 1000 حرف',
            'tags.array' => 'العلامات يجب أن تكون مصفوفة',
            'tags.*.max' => 'كل علامة لا يجب أن تتجاوز 50 حرف',
            'assigned_to.exists' => 'المستخدم المعين غير موجود',
            'next_follow_up_at.date' => 'تاريخ المتابعة التالية غير صحيح',
            'next_follow_up_at.after' => 'تاريخ المتابعة التالية يجب أن يكون في المستقبل',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'type' => 'نوع العميل',
            'name_ar' => 'الاسم بالعربية',
            'name_en' => 'الاسم بالإنجليزية',
            'email' => 'البريد الإلكتروني',
            'phone' => 'رقم الهاتف',
            'mobile' => 'رقم الجوال',
            'whatsapp' => 'رقم الواتساب',
            'birth_date' => 'تاريخ الميلاد',
            'gender' => 'الجنس',
            'nationality' => 'الجنسية',
            'id_number' => 'رقم الهوية',
            'company_name_ar' => 'اسم الشركة بالعربية',
            'company_name_en' => 'اسم الشركة بالإنجليزية',
            'commercial_register' => 'رقم السجل التجاري',
            'tax_number' => 'الرقم الضريبي',
            'industry' => 'القطاع',
            'company_size' => 'حجم الشركة',
            'website' => 'الموقع الإلكتروني',
            'country' => 'الدولة',
            'city' => 'المدينة',
            'district' => 'الحي',
            'address' => 'العنوان',
            'postal_code' => 'الرمز البريدي',
            'status' => 'الحالة',
            'priority' => 'الأولوية',
            'source' => 'المصدر',
            'referral_source' => 'مصدر الإحالة',
            'credit_limit' => 'حد الائتمان',
            'payment_terms' => 'شروط الدفع',
            'notes' => 'الملاحظات',
            'assigned_to' => 'المعين إلى',
            'next_follow_up_at' => 'تاريخ المتابعة التالية',
        ];
    }
}
