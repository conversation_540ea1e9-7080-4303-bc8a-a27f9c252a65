<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class TeamMember extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'employee_id',
        'department_id',
        'position_ar',
        'position_en',
        'employment_type',
        'status',
        'birth_date',
        'gender',
        'nationality',
        'id_number',
        'passport_number',
        'phone',
        'mobile',
        'emergency_contact_name',
        'emergency_contact_phone',
        'country',
        'city',
        'district',
        'address',
        'postal_code',
        'hire_date',
        'probation_end_date',
        'contract_end_date',
        'basic_salary',
        'currency',
        'benefits',
        'skills',
        'certifications',
        'notes',
        'direct_manager_id',
        'reports_to',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'hire_date' => 'date',
        'probation_end_date' => 'date',
        'contract_end_date' => 'date',
        'basic_salary' => 'decimal:2',
        'benefits' => 'array',
        'skills' => 'array',
        'certifications' => 'array',
        'reports_to' => 'array',
    ];

    /**
     * Get the user associated with this team member.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the department this team member belongs to.
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the direct manager.
     */
    public function directManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'direct_manager_id');
    }

    /**
     * Get the user who created this record.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this record.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get all attendance records for this team member.
     */
    public function attendance(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    /**
     * Get all leave requests for this team member.
     */
    public function leaveRequests(): HasMany
    {
        return $this->hasMany(LeaveRequest::class);
    }

    /**
     * Get all performance reviews for this team member.
     */
    public function performanceReviews(): HasMany
    {
        return $this->hasMany(PerformanceReview::class);
    }

    /**
     * Get salary history for this team member.
     */
    public function salaryHistory(): HasMany
    {
        return $this->hasMany(SalaryHistory::class);
    }

    /**
     * Get training records for this team member.
     */
    public function trainingRecords(): HasMany
    {
        return $this->hasMany(TrainingRecord::class);
    }

    /**
     * Scope to get active team members.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to filter by employment type.
     */
    public function scopeEmploymentType($query, string $type)
    {
        return $query->where('employment_type', $type);
    }

    /**
     * Scope to filter by department.
     */
    public function scopeInDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    /**
     * Get the team member's display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user->name ?? 'عضو فريق بدون اسم';
    }

    /**
     * Get the team member's position display name.
     */
    public function getPositionDisplayAttribute(): string
    {
        return $this->position_ar ?? $this->position_en ?? 'منصب غير محدد';
    }

    /**
     * Get the team member's full contact information.
     */
    public function getFullContactAttribute(): array
    {
        return [
            'email' => $this->user->email ?? null,
            'phone' => $this->phone,
            'mobile' => $this->mobile,
        ];
    }

    /**
     * Check if the team member is currently on probation.
     */
    public function isOnProbation(): bool
    {
        return $this->probation_end_date && $this->probation_end_date->isFuture();
    }

    /**
     * Get years of service.
     */
    public function getYearsOfServiceAttribute(): float
    {
        return $this->hire_date ? $this->hire_date->diffInYears(now()) : 0;
    }

    /**
     * Get months of service.
     */
    public function getMonthsOfServiceAttribute(): int
    {
        return $this->hire_date ? $this->hire_date->diffInMonths(now()) : 0;
    }

    /**
     * Check if contract is expiring soon (within 30 days).
     */
    public function isContractExpiringSoon(): bool
    {
        return $this->contract_end_date && 
               $this->contract_end_date->isFuture() && 
               $this->contract_end_date->diffInDays(now()) <= 30;
    }

    /**
     * Get the current salary information.
     */
    public function getCurrentSalary(): ?SalaryHistory
    {
        return $this->salaryHistory()
            ->where('effective_date', '<=', now())
            ->whereNull('end_date')
            ->orWhere('end_date', '>=', now())
            ->orderBy('effective_date', 'desc')
            ->first();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($teamMember) {
            if (empty($teamMember->employee_id)) {
                $teamMember->employee_id = static::generateEmployeeId();
            }
        });
    }

    /**
     * Generate a unique employee ID.
     */
    private static function generateEmployeeId(): string
    {
        $prefix = 'EMP';
        $year = date('Y');
        $lastMember = static::withTrashed()
            ->where('employee_id', 'like', $prefix . $year . '%')
            ->orderBy('employee_id', 'desc')
            ->first();

        if ($lastMember) {
            $lastNumber = (int) substr($lastMember->employee_id, strlen($prefix . $year));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
