<div>
    {{-- Header --}}
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 font-arabic">إدارة المنتجات والخدمات</h1>
                <p class="text-secondary-600 mt-1">إدارة كتالوج المنتجات والخدمات والمخزون</p>
            </div>
            @can('create', App\Models\Product::class)
                <button wire:click="openCreateModal" 
                        class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <x-icon name="plus" class="w-5 h-5 ml-2" />
                    إضافة منتج جديد
                </button>
            @endcan
        </div>
    </div>

    {{-- Search and Filters --}}
    <div class="bg-white rounded-lg shadow-sm border border-secondary-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {{-- Search --}}
            <div>
                <label class="block text-sm font-medium text-secondary-700 mb-1">البحث</label>
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <x-icon name="magnifying-glass" class="h-5 w-5 text-secondary-400" />
                    </div>
                    <input wire:model.live.debounce.300ms="search" 
                           type="text" 
                           class="block w-full pr-10 pl-3 py-2 border border-secondary-300 rounded-lg text-sm"
                           placeholder="البحث في المنتجات...">
                </div>
            </div>

            {{-- Category Filter --}}
            <div>
                <label class="block text-sm font-medium text-secondary-700 mb-1">الفئة</label>
                <select wire:model.live="categoryFilter" 
                        class="w-full px-3 py-2 border border-secondary-300 rounded-lg text-sm">
                    <option value="">جميع الفئات</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}">{{ $category->display_name }}</option>
                    @endforeach
                </select>
            </div>

            {{-- Status Filter --}}
            <div>
                <label class="block text-sm font-medium text-secondary-700 mb-1">الحالة</label>
                <select wire:model.live="statusFilter" 
                        class="w-full px-3 py-2 border border-secondary-300 rounded-lg text-sm">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="discontinued">متوقف</option>
                    <option value="out_of_stock">نفاد المخزون</option>
                </select>
            </div>

            {{-- Type Filter --}}
            <div>
                <label class="block text-sm font-medium text-secondary-700 mb-1">النوع</label>
                <select wire:model.live="typeFilter" 
                        class="w-full px-3 py-2 border border-secondary-300 rounded-lg text-sm">
                    <option value="">جميع الأنواع</option>
                    <option value="product">منتج</option>
                    <option value="service">خدمة</option>
                    <option value="digital">رقمي</option>
                    <option value="bundle">حزمة</option>
                </select>
            </div>
        </div>
    </div>

    {{-- Products Table --}}
    <div class="bg-white rounded-lg shadow-sm border border-secondary-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-secondary-200">
                <thead class="bg-secondary-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            المنتج
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            الفئة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            النوع
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            السعر
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            المخزون
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-secondary-200">
                    @forelse($products as $product)
                        <tr class="hover:bg-secondary-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-secondary-900">
                                        {{ $product->display_name }}
                                    </div>
                                    <div class="text-sm text-secondary-500">
                                        {{ $product->sku }}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-secondary-900">{{ $product->category->display_name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    {{ $product->type === 'product' ? 'bg-blue-100 text-blue-800' : 
                                       ($product->type === 'service' ? 'bg-green-100 text-green-800' : 
                                       ($product->type === 'digital' ? 'bg-purple-100 text-purple-800' : 'bg-orange-100 text-orange-800')) }}">
                                    {{ $product->type === 'product' ? 'منتج' : 
                                       ($product->type === 'service' ? 'خدمة' : 
                                       ($product->type === 'digital' ? 'رقمي' : 'حزمة')) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-secondary-900">{{ $product->formatted_selling_price }}</div>
                                <div class="text-xs text-secondary-500">التكلفة: {{ $product->formatted_cost_price }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($product->track_inventory)
                                    <div class="text-sm text-secondary-900">
                                        {{ number_format($product->current_stock) }} {{ $product->unit_display }}
                                    </div>
                                    @if($product->isLowStock())
                                        <div class="text-xs text-red-500">مخزون منخفض</div>
                                    @endif
                                @else
                                    <span class="text-secondary-400">غير متتبع</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    {{ $product->status === 'active' ? 'bg-green-100 text-green-800' : 
                                       ($product->status === 'inactive' ? 'bg-gray-100 text-gray-800' : 
                                       ($product->status === 'discontinued' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800')) }}">
                                    {{ $product->status === 'active' ? 'نشط' : 
                                       ($product->status === 'inactive' ? 'غير نشط' : 
                                       ($product->status === 'discontinued' ? 'متوقف' : 'نفاد المخزون')) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    @can('update', $product)
                                        <button wire:click="openEditModal({{ $product->id }})" 
                                                class="text-blue-600 hover:text-blue-900">
                                            <x-icon name="pencil" class="w-4 h-4" />
                                        </button>
                                        
                                        <button wire:click="toggleStatus({{ $product->id }})" 
                                                class="text-yellow-600 hover:text-yellow-900">
                                            <x-icon name="{{ $product->status === 'active' ? 'eye-slash' : 'eye' }}" class="w-4 h-4" />
                                        </button>
                                    @endcan
                                    
                                    @can('delete', $product)
                                        <button wire:click="deleteProduct({{ $product->id }})" 
                                                wire:confirm="هل أنت متأكد من حذف هذا المنتج؟"
                                                class="text-red-600 hover:text-red-900">
                                            <x-icon name="trash" class="w-4 h-4" />
                                        </button>
                                    @endcan
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <x-icon name="cube" class="w-12 h-12 text-secondary-400 mb-4" />
                                    <h3 class="text-lg font-medium text-secondary-900 mb-2">لا توجد منتجات</h3>
                                    <p class="text-secondary-500 mb-4">لم يتم العثور على أي منتجات مطابقة للبحث.</p>
                                    @can('create', App\Models\Product::class)
                                        <button wire:click="openCreateModal" 
                                                class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                            إضافة منتج جديد
                                        </button>
                                    @endcan
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        {{-- Pagination --}}
        @if($products->hasPages())
            <div class="px-6 py-3 border-t border-secondary-200">
                {{ $products->links() }}
            </div>
        @endif
    </div>

    {{-- Create/Edit Modal --}}
    @if($showModal)
        <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-secondary-500 bg-opacity-75 transition-opacity" wire:click="closeModal"></div>
                
                <div class="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                    <form wire:submit="save">
                        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                            <div class="mb-4">
                                <h3 class="text-lg font-medium text-secondary-900">
                                    {{ $editingProduct ? 'تعديل المنتج' : 'إضافة منتج جديد' }}
                                </h3>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {{-- Arabic Name --}}
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        اسم المنتج (عربي) <span class="text-red-500">*</span>
                                    </label>
                                    <input wire:model="name_ar" type="text" 
                                           class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                    @error('name_ar') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- English Name --}}
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        اسم المنتج (إنجليزي)
                                    </label>
                                    <input wire:model="name_en" type="text" 
                                           class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                    @error('name_en') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- Category --}}
                                <div>
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        الفئة <span class="text-red-500">*</span>
                                    </label>
                                    <select wire:model="category_id" 
                                            class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                        <option value="">اختر الفئة</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}">{{ $category->display_name }}</option>
                                        @endforeach
                                    </select>
                                    @error('category_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- Type --}}
                                <div>
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        النوع <span class="text-red-500">*</span>
                                    </label>
                                    <select wire:model="type" 
                                            class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                        <option value="product">منتج</option>
                                        <option value="service">خدمة</option>
                                        <option value="digital">رقمي</option>
                                        <option value="bundle">حزمة</option>
                                    </select>
                                    @error('type') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- Cost Price --}}
                                <div>
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        سعر التكلفة <span class="text-red-500">*</span>
                                    </label>
                                    <input wire:model="cost_price" type="number" step="0.01" min="0"
                                           class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                    @error('cost_price') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- Selling Price --}}
                                <div>
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        سعر البيع <span class="text-red-500">*</span>
                                    </label>
                                    <input wire:model="selling_price" type="number" step="0.01" min="0"
                                           class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                    @error('selling_price') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- Track Inventory --}}
                                <div class="md:col-span-2">
                                    <div class="flex items-center">
                                        <input wire:model="track_inventory" type="checkbox" 
                                               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded">
                                        <label class="mr-2 block text-sm text-secondary-900">
                                            تتبع المخزون
                                        </label>
                                    </div>
                                </div>

                                {{-- Stock Fields (shown only if track_inventory is true) --}}
                                @if($track_inventory)
                                    <div>
                                        <label class="block text-sm font-medium text-secondary-700 mb-1">
                                            المخزون الحالي <span class="text-red-500">*</span>
                                        </label>
                                        <input wire:model="current_stock" type="number" min="0"
                                               class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                        @error('current_stock') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-secondary-700 mb-1">
                                            الحد الأدنى للمخزون <span class="text-red-500">*</span>
                                        </label>
                                        <input wire:model="min_stock_level" type="number" min="0"
                                               class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                        @error('min_stock_level') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                    </div>
                                @endif

                                {{-- Unit --}}
                                <div>
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        الوحدة <span class="text-red-500">*</span>
                                    </label>
                                    <input wire:model="unit_ar" type="text" 
                                           class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                    @error('unit_ar') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- Status --}}
                                <div>
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        الحالة <span class="text-red-500">*</span>
                                    </label>
                                    <select wire:model="status" 
                                            class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                        <option value="discontinued">متوقف</option>
                                    </select>
                                    @error('status') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>

                                {{-- Description --}}
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-secondary-700 mb-1">
                                        وصف المنتج
                                    </label>
                                    <textarea wire:model="description_ar" rows="3" 
                                              class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"></textarea>
                                    @error('description_ar') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                </div>
                            </div>
                        </div>

                        <div class="bg-secondary-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                            <button type="submit" 
                                    class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mr-3 sm:w-auto sm:text-sm">
                                {{ $editingProduct ? 'تحديث' : 'إضافة' }}
                            </button>
                            <button type="button" wire:click="closeModal" 
                                    class="mt-3 w-full inline-flex justify-center rounded-lg border border-secondary-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-secondary-700 hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:mr-3 sm:w-auto sm:text-sm">
                                إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
</div>
