<?php

namespace App\Livewire\Clients;

use App\Models\Client;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Database\Eloquent\Builder;

class ClientList extends Component
{
    use WithPagination;

    // Search and Filter Properties
    public $search = '';
    public $typeFilter = '';
    public $statusFilter = '';
    public $priorityFilter = '';
    public $assignedToFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 15;

    // UI State
    public $showFilters = false;
    public $selectedClients = [];
    public $selectAll = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'typeFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'priorityFilter' => ['except' => ''],
        'assignedToFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
        'page' => ['except' => 1],
    ];

    public function mount()
    {
        $this->authorize('viewAny', Client::class);
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingTypeFilter()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingPriorityFilter()
    {
        $this->resetPage();
    }

    public function updatingAssignedToFilter()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->typeFilter = '';
        $this->statusFilter = '';
        $this->priorityFilter = '';
        $this->assignedToFilter = '';
        $this->resetPage();
    }

    public function toggleSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedClients = $this->getClientsQuery()->pluck('id')->toArray();
        } else {
            $this->selectedClients = [];
        }
    }

    public function deleteSelected()
    {
        $this->authorize('delete', Client::class);
        
        $count = count($this->selectedClients);
        Client::whereIn('id', $this->selectedClients)->delete();
        
        $this->selectedClients = [];
        $this->selectAll = false;
        
        session()->flash('success', "تم حذف {$count} عميل بنجاح");
        $this->resetPage();
    }

    public function deleteClient($clientId)
    {
        $client = Client::findOrFail($clientId);
        $this->authorize('delete', $client);
        
        $clientName = $client->display_name;
        $client->delete();
        
        session()->flash('success', "تم حذف العميل {$clientName} بنجاح");
        $this->resetPage();
    }

    public function getClientsQuery(): Builder
    {
        return Client::query()
            ->with(['assignedUser', 'creator'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name_ar', 'like', '%' . $this->search . '%')
                      ->orWhere('name_en', 'like', '%' . $this->search . '%')
                      ->orWhere('company_name_ar', 'like', '%' . $this->search . '%')
                      ->orWhere('company_name_en', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%')
                      ->orWhere('phone', 'like', '%' . $this->search . '%')
                      ->orWhere('mobile', 'like', '%' . $this->search . '%')
                      ->orWhere('client_code', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->typeFilter, function ($query) {
                $query->where('type', $this->typeFilter);
            })
            ->when($this->statusFilter, function ($query) {
                $query->where('status', $this->statusFilter);
            })
            ->when($this->priorityFilter, function ($query) {
                $query->where('priority', $this->priorityFilter);
            })
            ->when($this->assignedToFilter, function ($query) {
                $query->where('assigned_to', $this->assignedToFilter);
            })
            ->orderBy($this->sortField, $this->sortDirection);
    }

    public function getClientsProperty()
    {
        return $this->getClientsQuery()->paginate($this->perPage);
    }

    public function getUsersProperty()
    {
        return User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['founder', 'admin', 'manager', 'employee']);
        })->orderBy('name')->get();
    }

    public function render()
    {
        return view('livewire.clients.client-list', [
            'clients' => $this->clients,
            'users' => $this->users,
        ]);
    }
}
