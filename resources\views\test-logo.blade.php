<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>اختبار شعار الشركة - {{ __('app.company.name') }}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ asset('images/logo.png') }}">
    <link rel="apple-touch-icon" href="{{ asset('images/logo.png') }}">
    
    <!-- Arabic Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .demo-item {
            border: 2px dashed #e5e7eb;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
            background: #f9fafb;
        }
        
        .dark-bg {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
        }
        
        .code-block {
            background: #1f2937;
            color: #f3f4f6;
            padding: 1rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            margin: 0.5rem 0;
            overflow-x: auto;
        }
    </style>
</head>
<body class="font-arabic antialiased bg-secondary-50">
    <div class="min-h-screen py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-secondary-900 mb-2">اختبار تكامل شعار الشركة</h1>
                <p class="text-secondary-600">عرض جميع أشكال وأحجام الشعار المستخدمة في النظام</p>
            </div>
            
            <!-- Main Application Logo Variants -->
            <div class="demo-section">
                <h2 class="text-2xl font-bold text-secondary-900 mb-4">شعار التطبيق الرئيسي</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Default Logo -->
                    <div class="demo-item">
                        <h3 class="font-semibold mb-2">الشعار الافتراضي</h3>
                        <x-application-logo />
                        <div class="code-block mt-2">
&lt;x-application-logo /&gt;
                        </div>
                    </div>
                    
                    <!-- Large Logo -->
                    <div class="demo-item">
                        <h3 class="font-semibold mb-2">الشعار الكبير</h3>
                        <x-application-logo size="large" />
                        <div class="code-block mt-2">
&lt;x-application-logo size="large" /&gt;
                        </div>
                    </div>
                    
                    <!-- Small Logo -->
                    <div class="demo-item">
                        <h3 class="font-semibold mb-2">الشعار الصغير</h3>
                        <x-application-logo size="small" />
                        <div class="code-block mt-2">
&lt;x-application-logo size="small" /&gt;
                        </div>
                    </div>
                    
                    <!-- XL Logo -->
                    <div class="demo-item">
                        <h3 class="font-semibold mb-2">الشعار الكبير جداً</h3>
                        <x-application-logo size="xl" />
                        <div class="code-block mt-2">
&lt;x-application-logo size="xl" /&gt;
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Logo Variants -->
            <div class="demo-section">
                <h2 class="text-2xl font-bold text-secondary-900 mb-4">أشكال الشعار المختلفة</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Compact Variant -->
                    <div class="demo-item">
                        <h3 class="font-semibold mb-2">الشعار المضغوط</h3>
                        <x-application-logo variant="compact" />
                        <div class="code-block mt-2">
&lt;x-application-logo variant="compact" /&gt;
                        </div>
                    </div>
                    
                    <!-- Icon Only -->
                    <div class="demo-item">
                        <h3 class="font-semibold mb-2">الأيقونة فقط</h3>
                        <x-application-logo variant="icon-only" />
                        <div class="code-block mt-2">
&lt;x-application-logo variant="icon-only" /&gt;
                        </div>
                    </div>
                    
                    <!-- Text Only -->
                    <div class="demo-item">
                        <h3 class="font-semibold mb-2">النص فقط</h3>
                        <x-application-logo variant="text-only" />
                        <div class="code-block mt-2">
&lt;x-application-logo variant="text-only" /&gt;
                        </div>
                    </div>
                    
                    <!-- Non-clickable -->
                    <div class="demo-item">
                        <h3 class="font-semibold mb-2">غير قابل للنقر</h3>
                        <x-application-logo :clickable="false" />
                        <div class="code-block mt-2">
&lt;x-application-logo :clickable="false" /&gt;
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Email Logo -->
            <div class="demo-section">
                <h2 class="text-2xl font-bold text-secondary-900 mb-4">شعار البريد الإلكتروني</h2>
                
                <div class="demo-item">
                    <h3 class="font-semibold mb-2">شعار البريد الإلكتروني</h3>
                    <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                        <x-email-logo size="default" :showText="true" />
                    </div>
                    <div class="code-block mt-2">
&lt;x-email-logo size="default" :showText="true" /&gt;
                    </div>
                </div>
                
                <div class="demo-item dark-bg">
                    <h3 class="font-semibold mb-2">شعار البريد الإلكتروني (خلفية داكنة)</h3>
                    <x-email-logo size="default" :showText="true" class="logo-white" />
                    <div class="code-block mt-2">
&lt;x-email-logo size="default" :showText="true" class="logo-white" /&gt;
                    </div>
                </div>
            </div>
            
            <!-- Print Logo -->
            <div class="demo-section">
                <h2 class="text-2xl font-bold text-secondary-900 mb-4">شعار الطباعة</h2>
                
                <div class="demo-item">
                    <h3 class="font-semibold mb-2">شعار الطباعة</h3>
                    <x-print-logo size="default" :showText="true" />
                    <div class="code-block mt-2">
&lt;x-print-logo size="default" :showText="true" /&gt;
                    </div>
                </div>
                
                <div class="demo-item">
                    <h3 class="font-semibold mb-2">شعار الطباعة (بدون نص)</h3>
                    <x-print-logo size="default" :showText="false" />
                    <div class="code-block mt-2">
&lt;x-print-logo size="default" :showText="false" /&gt;
                    </div>
                </div>
            </div>
            
            <!-- Responsive Test -->
            <div class="demo-section">
                <h2 class="text-2xl font-bold text-secondary-900 mb-4">اختبار الاستجابة</h2>
                
                <div class="demo-item">
                    <h3 class="font-semibold mb-2">الشعار المتجاوب</h3>
                    <p class="text-sm text-secondary-600 mb-4">قم بتغيير حجم النافذة لرؤية كيفية تكيف الشعار</p>
                    
                    <!-- Desktop -->
                    <div class="hidden lg:block">
                        <p class="text-sm font-medium mb-2">سطح المكتب:</p>
                        <x-application-logo size="large" variant="default" />
                    </div>
                    
                    <!-- Tablet -->
                    <div class="hidden md:block lg:hidden">
                        <p class="text-sm font-medium mb-2">الجهاز اللوحي:</p>
                        <x-application-logo size="default" variant="compact" />
                    </div>
                    
                    <!-- Mobile -->
                    <div class="block md:hidden">
                        <p class="text-sm font-medium mb-2">الهاتف المحمول:</p>
                        <x-application-logo size="small" variant="icon-only" />
                    </div>
                </div>
            </div>
            
            <!-- Print Test -->
            <div class="demo-section no-print">
                <h2 class="text-2xl font-bold text-secondary-900 mb-4">اختبار الطباعة</h2>
                <p class="text-secondary-600 mb-4">اضغط على زر الطباعة لرؤية كيفية ظهور الشعار في الطباعة</p>
                <button onclick="window.print()" 
                        class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    طباعة الصفحة
                </button>
            </div>
            
            <!-- Print Header (only visible when printing) -->
            <div class="print-only" style="display: none;">
                <div class="print-header">
                    <x-print-logo size="default" :showText="true" />
                </div>
            </div>
            
            <!-- Navigation -->
            <div class="demo-section">
                <h2 class="text-2xl font-bold text-secondary-900 mb-4">التنقل</h2>
                <div class="flex space-x-4 rtl:space-x-reverse">
                    <a href="{{ route('dashboard') }}" 
                       class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        العودة إلى لوحة التحكم
                    </a>
                    <a href="{{ route('login') }}" 
                       class="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        صفحة تسجيل الدخول
                    </a>
                </div>
            </div>
            
        </div>
    </div>
    
    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            
            .print-only {
                display: block !important;
            }
            
            body {
                background: white !important;
            }
            
            .demo-section {
                box-shadow: none !important;
                border: 1px solid #ccc;
                page-break-inside: avoid;
            }
        }
    </style>
</body>
</html>
