<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add foreign key constraints that reference tables created later

        // Check if foreign key constraints already exist before adding them
        $invoicesForeignKeys = collect(DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'invoices'
            AND CONSTRAINT_NAME LIKE '%foreign%'
        "))->pluck('CONSTRAINT_NAME')->toArray();

        $expensesForeignKeys = collect(DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'expenses'
            AND CONSTRAINT_NAME LIKE '%foreign%'
        "))->pluck('CONSTRAINT_NAME')->toArray();

        // Finance module constraints
        if (!in_array('invoices_project_id_foreign', $invoicesForeignKeys)) {
            Schema::table('invoices', function (Blueprint $table) {
                $table->foreign('project_id')->references('id')->on('projects')->onDelete('set null');
            });
        }

        if (!in_array('expenses_project_id_foreign', $expensesForeignKeys)) {
            Schema::table('expenses', function (Blueprint $table) {
                $table->foreign('project_id')->references('id')->on('projects')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropForeign(['project_id']);
            $table->dropIndex(['project_id']);
        });

        Schema::table('expenses', function (Blueprint $table) {
            $table->dropForeign(['project_id']);
        });
    }
};
