<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" class="rtl">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Lial ERP') }} - {{ $title ?? __('app.dashboard.title') }}</title>
        <meta name="description" content="{{ __('app.company.description') }}">

        <!-- Favicon -->
        <link rel="icon" type="image/png" href="{{ asset('images/logo.png') }}">
        <link rel="apple-touch-icon" href="{{ asset('images/logo.png') }}">

        <!-- Arabic Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Additional Styles -->
        <style>
            [x-cloak] { display: none !important; }

            /* Print Styles */
            @media print {
                .no-print {
                    display: none !important;
                }

                .print-only {
                    display: block !important;
                }

                .print-header {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 2cm;
                    background: white;
                    border-bottom: 1px solid #ccc;
                    padding: 10px;
                    z-index: 1000;
                }

                .print-content {
                    margin-top: 2.5cm;
                }

                .sidebar {
                    display: none !important;
                }

                .main-content {
                    margin-left: 0 !important;
                    width: 100% !important;
                }

                /* Hide navigation and interactive elements */
                .fixed, .sticky, nav, .top-navigation, .sidebar {
                    display: none !important;
                }

                /* Ensure main content takes full width */
                .lg\\:mr-64 {
                    margin-right: 0 !important;
                }
            }
        </style>
    </head>
    <body class="font-arabic antialiased bg-secondary-50" x-data="{ sidebarOpen: false }">
        <div class="min-h-screen">
            <!-- Sidebar -->
            <div class="no-print fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
                 :class="{ 'translate-x-0': sidebarOpen, 'translate-x-full': !sidebarOpen }"
                 x-cloak>
                <x-sidebar />
            </div>

            <!-- Mobile sidebar overlay -->
            <div class="no-print fixed inset-0 z-40 bg-secondary-600 bg-opacity-75 transition-opacity duration-300 ease-linear lg:hidden"
                 x-show="sidebarOpen"
                 x-transition:enter="transition-opacity ease-linear duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="transition-opacity ease-linear duration-300"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 @click="sidebarOpen = false"
                 x-cloak>
            </div>

            <!-- Main content -->
            <div class="lg:mr-64">
                <!-- Top navigation -->
                <div class="no-print">
                    <x-top-navigation />
                </div>

                <!-- Page Heading -->
                @if (isset($header))
                    <header class="bg-white shadow-sm border-b border-secondary-200">
                        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
                            <div class="flex items-center justify-between">
                                <div>
                                    {{ $header }}
                                </div>
                                <div>
                                    <x-breadcrumb />
                                </div>
                            </div>
                        </div>
                    </header>
                @endif

                <!-- Page Content -->
                <main class="flex-1">
                    <div class="py-6">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <!-- Notifications -->
                            <x-notifications />

                            <!-- Main Content -->
                            {{ $slot }}
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- Scripts -->
        <script>
            // Global JavaScript for the application
            document.addEventListener('DOMContentLoaded', function() {
                // Auto-hide notifications after 5 seconds
                setTimeout(function() {
                    const notifications = document.querySelectorAll('[data-auto-hide]');
                    notifications.forEach(function(notification) {
                        notification.style.transition = 'opacity 0.5s ease-out';
                        notification.style.opacity = '0';
                        setTimeout(function() {
                            notification.remove();
                        }, 500);
                    });
                }, 5000);
            });
        </script>
    </body>
</html>
