<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>" class="rtl">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title><?php echo e(config('app.name', 'Lial ERP')); ?> - <?php echo e(__('app.auth.login_title')); ?></title>
        <meta name="description" content="<?php echo e(__('app.company.description')); ?>">

        <!-- Favicon -->
        <link rel="icon" type="image/png" href="<?php echo e(asset('images/logo.png')); ?>">
        <link rel="apple-touch-icon" href="<?php echo e(asset('images/logo.png')); ?>">

        <!-- Arabic Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    </head>
    <body class="font-arabic text-secondary-800 antialiased bg-gradient-to-br from-primary-50 via-white to-secondary-50">
        <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0">
            
            <div class="text-center mb-8">
                <?php if (isset($component)) { $__componentOriginal8892e718f3d0d7a916180885c6f012e7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8892e718f3d0d7a916180885c6f012e7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.application-logo','data' => ['size' => 'xl','variant' => 'default','clickable' => true,'showText' => true,'class' => 'mb-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('application-logo'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['size' => 'xl','variant' => 'default','clickable' => true,'showText' => true,'class' => 'mb-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8892e718f3d0d7a916180885c6f012e7)): ?>
<?php $attributes = $__attributesOriginal8892e718f3d0d7a916180885c6f012e7; ?>
<?php unset($__attributesOriginal8892e718f3d0d7a916180885c6f012e7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8892e718f3d0d7a916180885c6f012e7)): ?>
<?php $component = $__componentOriginal8892e718f3d0d7a916180885c6f012e7; ?>
<?php unset($__componentOriginal8892e718f3d0d7a916180885c6f012e7); ?>
<?php endif; ?>
                <p class="text-secondary-600 text-sm mt-2"><?php echo e(__('app.company.tagline')); ?></p>
            </div>

            
            <div class="w-full sm:max-w-md">
                <div class="bg-white shadow-xl rounded-2xl overflow-hidden border border-secondary-100">
                    
                    <div class="bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-4">
                        <h2 class="text-white text-lg font-semibold text-center">
                            <?php echo e($title ?? __('app.auth.login_title')); ?>

                        </h2>
                    </div>

                    
                    <div class="px-6 py-8">
                        <?php echo e($slot); ?>

                    </div>
                </div>

                
                <div class="text-center mt-6 text-sm text-secondary-500">
                    <p>&copy; <?php echo e(date('Y')); ?> <?php echo e(__('app.company.name')); ?>. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </body>
</html>
<?php /**PATH C:\laragon\www\lial_Erp\resources\views/layouts/guest.blade.php ENDPATH**/ ?>