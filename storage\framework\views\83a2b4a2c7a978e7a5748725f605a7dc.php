
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'size' => 'default', // small, default, large, xl
    'variant' => 'default', // default, compact, text-only, icon-only
    'clickable' => true,
    'showText' => true,
    'class' => ''
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'size' => 'default', // small, default, large, xl
    'variant' => 'default', // default, compact, text-only, icon-only
    'clickable' => true,
    'showText' => true,
    'class' => ''
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // Size configurations
    $sizeClasses = [
        'small' => 'h-8 w-auto',
        'default' => 'h-12 w-auto',
        'large' => 'h-16 w-auto',
        'xl' => 'h-20 w-auto'
    ];

    $logoClass = $sizeClasses[$size] ?? $sizeClasses['default'];

    // Text size configurations
    $textSizes = [
        'small' => ['title' => 'text-sm', 'subtitle' => 'text-xs'],
        'default' => ['title' => 'text-xl', 'subtitle' => 'text-xs'],
        'large' => ['title' => 'text-2xl', 'subtitle' => 'text-sm'],
        'xl' => ['title' => 'text-3xl', 'subtitle' => 'text-base']
    ];

    $textClass = $textSizes[$size] ?? $textSizes['default'];

    // Check for logo file existence
    $hasLogo = file_exists(public_path('images/logo.png')) || file_exists(public_path('logo/logo.png'));
    $logoPath = file_exists(public_path('images/logo.png')) ? 'images/logo.png' : 'logo/logo.png';
?>

<div class="logo-container flex items-center <?php echo e($variant === 'compact' ? 'justify-start' : 'justify-center'); ?> <?php echo e($class); ?>" <?php echo e($attributes); ?>>
    <?php if($clickable): ?>
        <a href="<?php echo e(auth()->check() ? route('dashboard') : '/'); ?>"
           class="flex items-center <?php echo e($variant === 'compact' ? 'space-x-2 rtl:space-x-reverse' : ($showText ? 'space-x-3 rtl:space-x-reverse' : '')); ?> transition-opacity hover:opacity-80"
           wire:navigate>
    <?php else: ?>
        <div class="flex items-center <?php echo e($variant === 'compact' ? 'space-x-2 rtl:space-x-reverse' : ($showText ? 'space-x-3 rtl:space-x-reverse' : '')); ?>">
    <?php endif; ?>

        
        <?php if($variant !== 'text-only'): ?>
            <?php if($hasLogo): ?>
                <img src="<?php echo e(asset($logoPath)); ?>"
                     alt="<?php echo e(__('app.company.name')); ?>"
                     class="<?php echo e($logoClass); ?> object-contain"
                     loading="lazy">
            <?php else: ?>
                
                <svg class="<?php echo e(str_replace('w-auto', 'w-' . explode('-', $logoClass)[1], $logoClass)); ?> text-primary-600 flex-shrink-0"
                     viewBox="0 0 100 100"
                     xmlns="http://www.w3.org/2000/svg"
                     aria-label="<?php echo e(__('app.company.name')); ?>">
                    <defs>
                        <linearGradient id="lialGradient-<?php echo e(uniqid()); ?>" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    
                    <circle cx="50" cy="50" r="45" fill="url(#lialGradient-<?php echo e(uniqid()); ?>)" opacity="0.1"/>
                    <path d="M25 25 L75 25 L75 35 L35 35 L35 75 L25 75 Z" fill="url(#lialGradient-<?php echo e(uniqid()); ?>)"/>
                    <path d="M45 45 L75 45 L75 55 L55 55 L55 75 L45 75 Z" fill="url(#lialGradient-<?php echo e(uniqid()); ?>)" opacity="0.8"/>
                    <circle cx="65" cy="65" r="8" fill="url(#lialGradient-<?php echo e(uniqid()); ?>)"/>
                </svg>
            <?php endif; ?>
        <?php endif; ?>

        
        <?php if($showText && $variant !== 'icon-only'): ?>
            <div class="text-right rtl:text-right <?php echo e($variant === 'compact' ? 'hidden lg:block' : ''); ?>">
                <div class="<?php echo e($textClass['title']); ?> font-bold text-primary-600 font-arabic leading-tight">
                    <?php echo e(__('app.company.name_short', [], 'ar') ?? 'ليال'); ?>

                </div>
                <?php if($variant !== 'compact'): ?>
                    <div class="<?php echo e($textClass['subtitle']); ?> text-secondary-500 font-arabic leading-tight">
                        <?php echo e(__('app.company.tagline_short', [], 'ar') ?? 'للتطوير البرمجي'); ?>

                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

    <?php if($clickable): ?>
        </a>
    <?php else: ?>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH C:\laragon\www\lial_Erp\resources\views/components/application-logo.blade.php ENDPATH**/ ?>